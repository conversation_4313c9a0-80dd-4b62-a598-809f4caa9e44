import os
import re
from datetime import datetime
from typing import List, Optional

import pandas as pd
from databricks import sql
from dotenv import load_dotenv


# -----------------------------------------------------------------------------
# Environment & Connection Helpers
# -----------------------------------------------------------------------------

# Attempt to load environment variables from a .env file if present
load_dotenv("Germany_Scripts/.env", override=True)


def _dbg(msg: str) -> None:
    """Light-weight debug print helper."""
    print(f"[MB5L-Explorer] {msg}")


def get_connection():
    """Return a Databricks SQL connection using environment variables.

    Required environment variables:
        DATABRICKS_HOST      e.g. https://<workspace>.databricks.com
        DATABRICKS_HTTP_PATH e.g. /sql/1.0/warehouses/<wh-id>
        DATABRICKS_TOKEN     personal-access-token
    """
    host = "adb-671640162813626.6.azuredatabricks.net"
    http_path = "/sql/1.0/warehouses/b22260fd910a310c"
    token = "**************************************"

    if not all([host, http_path, token]):
        raise EnvironmentError(
            "Missing one or more required environment variables: "
            "DATABRICKS_HOST, DATABRICKS_HTTP_PATH, DATABRICKS_TOKEN"
        )

    _dbg("Opening Databricks SQL connection …")
    return sql.connect(server_hostname=host, http_path=http_path, access_token=token)


# -----------------------------------------------------------------------------
# Metadata Discovery
# -----------------------------------------------------------------------------

CATALOG = os.getenv("SAP_CATALOG", "brewdat_uc_europe_prod")
SCHEMA = os.getenv("SAP_SCHEMA", "slv_eur_tech_sap_ecc_europe")


def list_tables_like(pattern: str, catalog: str = CATALOG, schema: str = SCHEMA) -> pd.DataFrame:
    """Return a DataFrame of tables/views whose names match *pattern* (regex)."""
    query = f"SHOW TABLES IN {catalog}.{schema}"
    regex = re.compile(pattern, flags=re.IGNORECASE)

    with get_connection() as conn, conn.cursor() as cur:
        cur.execute(query)
        tbl_df = cur.fetchall_arrow().to_pandas()

    # Databricks returns columns: database, tableName, isTemporary
    tbl_df = tbl_df.rename(columns=str.lower)
    matches = tbl_df[tbl_df["tablename"].str.match(regex)]
    return matches.reset_index(drop=True)


# -----------------------------------------------------------------------------
# Data Extraction
# -----------------------------------------------------------------------------

DATE_CANDIDATE_COLS = (
    "entry_date",
    "budat",        # posting date
    "bldat",        # document date
    "cpudt_mkpf",   # creation date (in some ECC extracts)
)


def _detect_date_col(columns: List[str]) -> Optional[str]:
    """Return first column name that looks like a date value."""
    for cand in DATE_CANDIDATE_COLS:
        for col in columns:
            if col.lower() == cand.lower():
                return col
    # Fallback: any column that ends with "date"
    for col in columns:
        if col.lower().endswith("date"):
            return col
    return None


def fetch_material_data(table_fqn: str, matnr: str, target_date: datetime) -> pd.DataFrame:
    """Fetch rows for *matnr* on *target_date* from *table_fqn*.

    The function tries to guess a suitable date column automatically.
    """
    _dbg(f"Fetching columns for {table_fqn} …")
    with get_connection() as conn, conn.cursor() as cur:
        # DESCRIBE result has schema of col_name, data_type, comment
        cur.execute(f"DESCRIBE TABLE {table_fqn}")
        cols_df = cur.fetchall_arrow().to_pandas()

    cols = cols_df.iloc[:, 0].str.strip().tolist()
    date_col = _detect_date_col(cols)
    if not date_col:
        raise ValueError(
            f"Could not detect a date column in {table_fqn}. "
            f"Columns: {', '.join(cols[:20])} …"
        )

    if "matnr" not in (c.lower() for c in cols):
        raise ValueError("Table does not contain a MATNR (material) column – aborting.")

    date_literal = target_date.strftime("%Y-%m-%d")
    query = (
        f"SELECT * FROM {table_fqn} "
        f"WHERE matnr = '{matnr}' AND {date_col} = DATE('{date_literal}')"
    )

    _dbg(f"Running data query on {table_fqn} (date column: {date_col}) …")
    with get_connection() as conn, conn.cursor() as cur:
        cur.execute(query)
        result_df = cur.fetchall_arrow().to_pandas()

    _dbg(f"Retrieved {len(result_df)} rows from {table_fqn}.")
    return result_df


# -----------------------------------------------------------------------------
# CLI-style usage
# -----------------------------------------------------------------------------

def main():
    """Search for MB5L-like tables and extract data for the requested material/date."""
    search_regex = r".*mb5l.*"  # case-insensitive regex
    material = os.getenv("TARGET_MATNR", "7602171")
    target_date_str = os.getenv("TARGET_DATE", "2025-07-01")
    target_date = datetime.strptime(target_date_str, "%Y-%m-%d")

    _dbg(
        f"Searching for tables in {CATALOG}.{SCHEMA} matching regex '{search_regex}' …"
    )
    matches = list_tables_like(search_regex)

    if matches.empty:
        _dbg("No tables/views matched the MB5L pattern. Please adjust the regex or schema.")
        return

    print("\nCandidate tables/views:")
    print(matches[["database", "tablename"]].to_string(index=False))

    # Attempt extraction from the first match by default
    table_fqn = f"{CATALOG}.{SCHEMA}.{matches.loc[0, 'tablename']}"

    try:
        df = fetch_material_data(table_fqn, material, target_date)
    except Exception as exc:
        _dbg(str(exc))
        return

    if df.empty:
        _dbg(
            f"No records found for MATNR {material} on {target_date_str} in {table_fqn}."
        )
    else:
        # Basic summary
        print("\nData snapshot (first 5 rows):")
        print(df.head())
        output_path = f"{material}_{target_date_str}_mb5l_extract.parquet"
        df.to_parquet(output_path, index=False)
        _dbg(f"Saved {len(df)} rows → {output_path}")


if __name__ == "__main__":
    main()
