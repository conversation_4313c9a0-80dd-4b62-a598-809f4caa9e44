import pandas as pd
import os
import logging
from datetime import date

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_script_output_skus():
    """
    Load SKUs from the script output file.
    """
    try:
        # Look for the most recent PBI output file
        output_filename = f'PBI_Output_{date.today().strftime("%Y-%m-%d")}.xlsx'
        
        if os.path.exists(output_filename):
            logging.info(f"Loading SKUs from {output_filename}...")
            df = pd.read_excel(output_filename, sheet_name='PowerBI_upload')
            script_skus = set(df['SKU'].unique())
            logging.info(f"Found {len(script_skus)} unique SKUs in script output")
            return script_skus, df
        else:
            logging.error(f"Script output file not found: {output_filename}")
            return set(), pd.DataFrame()
            
    except Exception as e:
        logging.error(f"Error loading script output: {e}")
        return set(), pd.DataFrame()

def load_category_mapping_skus():
    """
    Load SKUs from multiple category mapping sources.
    """
    category_data = {}
    
    # 1. Load from DE Temp POCM Excel file (Databricks notebook source)
    try:
        excel_file = r'C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\GermanyImpairmentRevamp\DE Temp POCM Calculation P07 2025_TL.xlsx'
        
        if os.path.exists(excel_file):
            logging.info("Loading category mappings from DE Temp POCM Excel file...")
            
            # Load Category sheet
            try:
                cat_df = pd.read_excel(excel_file, sheet_name='Category')
                
                # Find SKU column
                sku_col = None
                category_col = None
                
                for col in cat_df.columns:
                    if 'sku' in str(col).lower():
                        sku_col = col
                    if 'category' in str(col).lower() or 'impairment' in str(col).lower():
                        category_col = col
                
                if sku_col and category_col:
                    cat_clean = cat_df[[sku_col, category_col]].dropna()
                    cat_clean.columns = ['SKU', 'Category']
                    category_data['POCM_Category'] = {
                        'skus': set(cat_clean['SKU'].astype(str).unique()),
                        'df': cat_clean,
                        'categories': cat_clean['Category'].unique(),
                        'source': 'DE Temp POCM - Category Sheet'
                    }
                    logging.info(f"POCM Category sheet: {len(category_data['POCM_Category']['skus'])} SKUs, Categories: {list(category_data['POCM_Category']['categories'])}")
                else:
                    logging.warning(f"POCM Category sheet: Could not find SKU/Category columns. Available: {list(cat_df.columns)}")
                    
            except Exception as e:
                logging.error(f"Error reading POCM Category sheet: {e}")
            
            # Load Category 2 sheet
            try:
                cat2_df = pd.read_excel(excel_file, sheet_name='Category 2')
                
                # Find SKU column
                sku_col = None
                category_col = None
                
                for col in cat2_df.columns:
                    if 'sku' in str(col).lower():
                        sku_col = col
                    if 'category' in str(col).lower() or 'impairment' in str(col).lower():
                        category_col = col
                
                if sku_col and category_col:
                    cat2_clean = cat2_df[[sku_col, category_col]].dropna()
                    cat2_clean.columns = ['SKU', 'Category']
                    category_data['POCM_Category2'] = {
                        'skus': set(cat2_clean['SKU'].astype(str).unique()),
                        'df': cat2_clean,
                        'categories': cat2_clean['Category'].unique(),
                        'source': 'DE Temp POCM - Category 2 Sheet'
                    }
                    logging.info(f"POCM Category 2 sheet: {len(category_data['POCM_Category2']['skus'])} SKUs, Categories: {list(category_data['POCM_Category2']['categories'])}")
                else:
                    logging.warning(f"POCM Category 2 sheet: Could not find SKU/Category columns. Available: {list(cat2_df.columns)}")
                    
            except Exception as e:
                logging.error(f"Error reading POCM Category 2 sheet: {e}")
        else:
            logging.warning(f"DE Temp POCM file not found: {excel_file}")
    
    except Exception as e:
        logging.error(f"Error loading POCM category mappings: {e}")
    
    # 2. Load from Jupyter Notebook source (Current Stock Central file)
    try:
        jupyter_category_file = r'C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Germany Impairment\Category\Current Stock Central - Impairment Category.xlsx'
        
        if os.path.exists(jupyter_category_file):
            logging.info("Loading category mappings from Jupyter notebook source (Current Stock Central)...")
            
            try:
                jupyter_cat_df = pd.read_excel(jupyter_category_file)
                
                # Find SKU and Category columns
                sku_col = None
                category_col = None
                
                for col in jupyter_cat_df.columns:
                    if 'sku' in str(col).lower():
                        sku_col = col
                    if 'category' in str(col).lower() or 'impairment' in str(col).lower():
                        category_col = col
                
                if sku_col and category_col:
                    jupyter_clean = jupyter_cat_df[[sku_col, category_col]].dropna()
                    jupyter_clean.columns = ['SKU', 'Category']
                    category_data['Jupyter_Category'] = {
                        'skus': set(jupyter_clean['SKU'].astype(str).unique()),
                        'df': jupyter_clean,
                        'categories': jupyter_clean['Category'].unique(),
                        'source': 'Current Stock Central - Impairment Category.xlsx'
                    }
                    logging.info(f"Jupyter Category file: {len(category_data['Jupyter_Category']['skus'])} SKUs, Categories: {list(category_data['Jupyter_Category']['categories'])}")
                else:
                    logging.warning(f"Jupyter Category file: Could not find SKU/Category columns. Available: {list(jupyter_cat_df.columns)}")
                    
            except Exception as e:
                logging.error(f"Error reading Jupyter category file: {e}")
        else:
            logging.warning(f"Jupyter category file not found: {jupyter_category_file}")
    
    except Exception as e:
        logging.error(f"Error loading Jupyter category mappings: {e}")
    
    # 3. Load Status mapping from Jupyter notebook source
    try:
        jupyter_status_file = r'C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Germany Impairment\Category\Status.xlsx'
        
        if os.path.exists(jupyter_status_file):
            logging.info("Loading status mappings from Jupyter notebook source...")
            
            try:
                status_df = pd.read_excel(jupyter_status_file)
                category_data['Status_Mapping'] = {
                    'df': status_df,
                    'source': 'Status.xlsx',
                    'columns': list(status_df.columns)
                }
                logging.info(f"Status mapping file loaded with columns: {list(status_df.columns)}")
                
            except Exception as e:
                logging.error(f"Error reading status mapping file: {e}")
        else:
            logging.warning(f"Status mapping file not found: {jupyter_status_file}")
    
    except Exception as e:
        logging.error(f"Error loading status mappings: {e}")
        
    return category_data

def analyze_sku_matching():
    """
    Perform comprehensive SKU matching analysis.
    """
    logging.info("=" * 60)
    logging.info("SKU MATCHING ANALYSIS")
    logging.info("=" * 60)
    
    # Load data
    script_skus, script_df = load_script_output_skus()
    category_data = load_category_mapping_skus()
    
    if not script_skus:
        logging.error("No script output data found. Please run script2.py first.")
        return
    
    if not category_data:
        logging.error("No category mapping data found.")
        return
    
    # Convert script SKUs to strings for comparison
    script_skus_str = {str(sku) for sku in script_skus}
    
    print("\n" + "=" * 80)
    print("📊 SKU MATCHING ANALYSIS RESULTS")
    print("=" * 80)
    
    # Overall statistics
    print(f"\n🔍 OVERALL STATISTICS:")
    print(f"   Script Output SKUs: {len(script_skus_str):,}")
    
    # Analyze each category source
    all_category_skus = set()
    matched_by_sheet = {}
    
    for sheet_name, sheet_data in category_data.items():
        if 'skus' in sheet_data and sheet_data['skus']:
            all_category_skus.update(sheet_data['skus'])
            
            # Find matches
            matched = script_skus_str.intersection(sheet_data['skus'])
            unmatched_script = script_skus_str - sheet_data['skus']
            unmatched_category = sheet_data['skus'] - script_skus_str
            
            matched_by_sheet[sheet_name] = {
                'matched': matched,
                'unmatched_script': unmatched_script,
                'unmatched_category': unmatched_category
            }
            
            print(f"\n📋 {sheet_name.upper().replace('_', ' ')} ANALYSIS:")
            print(f"   Source: {sheet_data['source']}")
            print(f"   Category SKUs: {len(sheet_data['skus']):,}")
            if 'categories' in sheet_data:
                print(f"   Categories: {', '.join(sheet_data['categories'])}")
            print(f"   ✅ Matched SKUs: {len(matched):,} ({len(matched)/len(script_skus_str)*100:.1f}% of script output)")
            print(f"   ❌ Script SKUs not in {sheet_name}: {len(unmatched_script):,}")
            print(f"   ⚠️  {sheet_name} SKUs not in script: {len(unmatched_category):,}")
        elif sheet_name == 'Status_Mapping':
            print(f"\n📋 STATUS MAPPING ANALYSIS:")
            print(f"   Source: {sheet_data['source']}")
            print(f"   Columns: {', '.join(sheet_data['columns'])}")
            print(f"   Rows: {len(sheet_data['df']):,}")
    
    # Combined analysis
    if all_category_skus:
        combined_matched = script_skus_str.intersection(all_category_skus)
        combined_unmatched_script = script_skus_str - all_category_skus
        
        print(f"\n🔄 COMBINED ANALYSIS (All Category Sources):")
        print(f"   Total Unique Category SKUs: {len(all_category_skus):,}")
        print(f"   ✅ Script SKUs with Category Mapping: {len(combined_matched):,} ({len(combined_matched)/len(script_skus_str)*100:.1f}%)")
        print(f"   ❌ Script SKUs WITHOUT Category Mapping: {len(combined_unmatched_script):,} ({len(combined_unmatched_script)/len(script_skus_str)*100:.1f}%)")
        
        # Show overlap between different sources
        if len([k for k in category_data.keys() if 'skus' in category_data[k]]) > 1:
            print(f"\n🔀 SOURCE OVERLAP ANALYSIS:")
            source_names = [k for k in category_data.keys() if 'skus' in category_data[k]]
            for i, source1 in enumerate(source_names):
                for source2 in source_names[i+1:]:
                    overlap = category_data[source1]['skus'].intersection(category_data[source2]['skus'])
                    unique_1 = category_data[source1]['skus'] - category_data[source2]['skus']
                    unique_2 = category_data[source2]['skus'] - category_data[source1]['skus']
                    
                    print(f"   {source1} vs {source2}:")
                    print(f"     Common SKUs: {len(overlap):,}")
                    print(f"     Only in {source1}: {len(unique_1):,}")
                    print(f"     Only in {source2}: {len(unique_2):,}")
    
    # Create detailed report
    create_detailed_report(script_df, category_data, matched_by_sheet, all_category_skus)
    
    print(f"\n📄 Detailed report saved to: SKU_Matching_Analysis_{date.today().strftime('%Y-%m-%d')}.xlsx")
    print("=" * 80)

def create_detailed_report(script_df, category_data, matched_by_sheet, all_category_skus):
    """
    Create detailed Excel report with matching analysis.
    """
    try:
        output_file = f"SKU_Matching_Analysis_{date.today().strftime('%Y-%m-%d')}.xlsx"
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            
            # Summary sheet
            summary_data = []
            
            script_skus_str = {str(sku) for sku in script_df['SKU'].unique()}
            combined_matched = script_skus_str.intersection(all_category_skus)
            combined_unmatched = script_skus_str - all_category_skus
            
            summary_data.append(['Total Script SKUs', len(script_skus_str)])
            summary_data.append(['Total Category SKUs (Combined)', len(all_category_skus)])
            summary_data.append(['Matched SKUs', len(combined_matched)])
            summary_data.append(['Unmatched Script SKUs', len(combined_unmatched)])
            summary_data.append(['Match Rate (%)', f"{len(combined_matched)/len(script_skus_str)*100:.1f}%"])
            
            for sheet_name, match_data in matched_by_sheet.items():
                summary_data.append([f'{sheet_name} - Matched', len(match_data['matched'])])
                summary_data.append([f'{sheet_name} - Match Rate (%)', f"{len(match_data['matched'])/len(script_skus_str)*100:.1f}%"])
            
            summary_df = pd.DataFrame(summary_data, columns=['Metric', 'Value'])
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # Script data with match status
            script_analysis = script_df.copy()
            script_analysis['SKU_str'] = script_analysis['SKU'].astype(str)
            script_analysis['Has_Category_Mapping'] = script_analysis['SKU_str'].isin(all_category_skus)
            
            # Add specific sheet matches
            for sheet_name, sheet_data in category_data.items():
                script_analysis[f'In_{sheet_name.replace(" ", "_")}'] = script_analysis['SKU_str'].isin(sheet_data['skus'])
            
            script_analysis.drop('SKU_str', axis=1, inplace=True)
            script_analysis.to_excel(writer, sheet_name='Script_Data_Analysis', index=False)
            
            # Unmatched SKUs
            unmatched_skus = list(script_skus_str - all_category_skus)
            if unmatched_skus:
                unmatched_df = pd.DataFrame({'Unmatched_SKUs': unmatched_skus})
                unmatched_df.to_excel(writer, sheet_name='Unmatched_SKUs', index=False)
            
            # Category sheets data
            for sheet_name, sheet_data in category_data.items():
                if not sheet_data['df'].empty:
                    sheet_data['df'].to_excel(writer, sheet_name=f'{sheet_name.replace(" ", "_")}_Data', index=False)
        
        logging.info(f"Detailed report created: {output_file}")
        
    except Exception as e:
        logging.error(f"Error creating detailed report: {e}")

if __name__ == '__main__':
    try:
        analyze_sku_matching()
    except Exception as e:
        logging.error(f"Analysis failed: {e}")
        print(f"\n❌ Analysis failed: {e}")
