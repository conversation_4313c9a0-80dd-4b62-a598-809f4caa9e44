import os
import pandas as pd
from databricks import sql
from dotenv import load_dotenv
import numpy as np

# Load environment variables from a .env file for security
load_dotenv()

def get_databricks_connection():
    """Establishes and returns a connection to the Databricks SQL warehouse."""
    try:
        connection = sql.connect(
            server_hostname=os.getenv("DATABRICKS_HOST"),
            http_path=os.getenv("DATABRICKS_HTTP_PATH"),
            access_token=os.getenv("DATABRICKS_TOKEN")
        )
        print("✅ Successfully connected to Databricks.")
        return connection
    except Exception as e:
        print(f"❌ Failed to connect to Databricks: {e}")
        return None

def get_historical_inventory(material_list_df, snapshot_date):
    """
    Reconstructs inventory balances for a specific historical date by reversing
    subsequent movements.
    """
    material_tuple_list = [
        f"('{row['ValA']}', '{str(row['Material']).zfill(18)}')" 
        for index, row in material_list_df.iterrows()
    ]
    if not material_tuple_list:
        print("⚠️ No material pairs found in the Excel file to query.")
        return None
    values_clause = ", ".join(material_tuple_list)

    # This query uses a CTE to calculate the net change after the snapshot date.
    # FIX: Corrected the join on the 'makt' table to use the fully qualified name.
    query = f"""
    WITH SubsequentMovements AS (
        -- Step 1: Calculate all movements that occurred AFTER the snapshot date.
        SELECT
            matnr,
            werks,
            SUM(
                CASE WHEN shkzg = 'S' THEN menge WHEN shkzg = 'H' THEN -1 * menge ELSE 0 END
            ) AS net_quantity_change,
            SUM(
                 CASE WHEN shkzg = 'S' THEN dmbtr WHEN shkzg = 'H' THEN -1 * dmbtr ELSE 0 END
            ) AS net_value_change
        FROM
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        WHERE
            cpudt_mkpf > '{snapshot_date}' AND (werks, matnr) IN ({values_clause})
        GROUP BY
            matnr, werks
    )
    -- Step 2: Join current balances with the subsequent movements and reverse them.
    SELECT
        mbew.bwkey AS plant_code,
        mbew.matnr AS material,
        makt.maktx AS material_description,
        (mbew.lbkum - COALESCE(sm.net_quantity_change, 0)) AS total_stock,
        (mbew.salk3 - COALESCE(sm.net_value_change, 0)) AS total_value
    FROM
        brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS mbew
    LEFT JOIN
        brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS makt -- <<< FIX WAS HERE
            ON mbew.matnr = makt.matnr AND makt.spras = 'E'
    LEFT JOIN
        SubsequentMovements sm 
            ON mbew.matnr = sm.matnr AND mbew.bwkey = sm.werks
    WHERE
        (mbew.bwkey, mbew.matnr) IN ({values_clause});
    """
    
    connection = get_databricks_connection()
    if not connection: return None
        
    try:
        print(f"\n🚀 Reconstructing inventory balances for {snapshot_date} (Corrected Query)...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            result = cursor.fetchall_arrow()
            df = result.to_pandas()
            df['total_stock'] = pd.to_numeric(df['total_stock'], errors='coerce').fillna(0)
            df['total_value'] = pd.to_numeric(df['total_value'], errors='coerce').fillna(0)
            df['material'] = df['material'].str.lstrip('0')
            print(f"✅ Query successful. Retrieved {len(df)} historical records.")
            return df
    except Exception as e:
        print(f"❌ Error executing Databricks query: {e}")
        return None
    finally:
        if connection:
            connection.close()
            print("Connection to Databricks closed.")

def validate_results(queried_df, excel_file_path):
    """
    Validates the reconstructed data against the original Excel/CSV file.
    """
    if queried_df is None or queried_df.empty:
        print("⚠️ Cannot perform validation, the queried data is empty.")
        return

    print(f"\n🔍 Loading and cleaning validation file: {excel_file_path}")
    excel_df = pd.read_csv(excel_file_path)
    excel_df.columns = excel_df.columns.str.strip()
    excel_df.dropna(subset=['Material'], inplace=True)
    excel_df['Material'] = excel_df['Material'].astype(int).astype(str)

    numeric_cols = ['Total Stock', 'Total Value']
    for col in numeric_cols:
        if excel_df[col].dtype == 'object':
            excel_df[col] = pd.to_numeric(excel_df[col].astype(str).str.replace(',', ''), errors='coerce')
    
    excel_df[numeric_cols] = excel_df[numeric_cols].fillna(0)

    queried_total_stock = queried_df['total_stock'].sum()
    queried_total_value = queried_df['total_value'].sum()
    excel_total_stock = excel_df['Total Stock'].sum()
    excel_total_value = excel_df['Total Value'].sum()

    print("\n--- Final Validation Summary ---")
    print(f"| Metric      | Reconstructed Value | Excel Value        | Match     |")
    print(f"|-------------|---------------------|--------------------|-----------|")
    stock_match = '✅ Yes' if np.isclose(queried_total_stock, excel_total_stock, atol=1) else '❌ No'
    value_match = '✅ Yes' if np.isclose(queried_total_value, excel_total_value, atol=1) else '❌ No'
    print(f"| Total Stock | {queried_total_stock:19,.2f} | {excel_total_stock:18,.2f} | {stock_match.ljust(9)} |")
    print(f"| Total Value | {queried_total_value:19,.2f} | {excel_total_value:18,.2f} | {value_match.ljust(9)} |")
    print("----------------------------------------------------------------------\n")

def main():
    """Main function to run the process and generate the reconciliation report."""
    excel_file = 'MB5L.xlsx - Sheet1.csv'
    snapshot_date = '2025-08-31'
    
    try:
        material_list_df = pd.read_csv(excel_file)
        material_list_df.columns = material_list_df.columns.str.strip()
        material_list_df.dropna(subset=['ValA', 'Material'], inplace=True)
        material_list_df['Material'] = material_list_df['Material'].astype(int)
        print(f"Found {len(material_list_df)} material/plant combinations in {excel_file} to query.")
    except FileNotFoundError:
        print(f"❌ Error: The file {excel_file} was not found.")
        return

    inventory_df = get_historical_inventory(material_list_df, snapshot_date)
    
    if inventory_df is not None and not inventory_df.empty:
        validate_results(inventory_df, excel_file)

if __name__ == "__main__":
    main()