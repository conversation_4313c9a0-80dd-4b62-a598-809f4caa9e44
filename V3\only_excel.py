import pandas as pd
import numpy as np

# --- Configuration ---
# The detailed calculation sheets are our sole source of truth for this test.
VALIDATION_FILES = [
    'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 09.csv',
    'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 36.csv'
]

# Manually define column headers for robust parsing of the messy CSV files
MB5B_COLUMN_NAMES = [
    'Conc', 'Plant', 'Material', 'Material Description', 'Opening Stock', 'Total Receipt Qties', 
    'Issue Quantiti', 'Closing Stock', 'QTES', 'CTRL ecart', 'Valeur', 
    'Valeur/qtes', 'Opening - issue quantity >0', 'Impairment', 'NBV', 'Category'
]

def main():
    """
    Tests the hypothesis that Impairment = (Opening Stock - Issues) * Avg Price,
    using only the data within the MB5B source files.
    """
    print("--- Verifying Impairment Calculation Logic ---")
    
    all_dfs = []
    print("STEP 1: Loading and parsing the detailed MB5B files...")
    for file in VALIDATION_FILES:
        try:
            df = pd.read_csv(
                file, 
                skiprows=5, 
                header=None, 
                names=MB5B_COLUMN_NAMES, 
                thousands=',',
                dtype={'Material': str}
            )
            df.dropna(subset=['Material', 'Plant'], inplace=True)
            all_dfs.append(df)
        except Exception as e:
            print(f"❌ ERROR: Could not process file '{file}': {e}")
            return
            
    # Combine data from both the 9-month and 36-month files
    combined_df = pd.concat(all_dfs, ignore_index=True)
    print(f"✅ Loaded a total of {len(combined_df)} records for analysis.")

    # STEP 2: Clean the necessary columns and ensure they are numeric
    cols_to_numeric = ['Opening Stock', 'Issue Quantiti', 'Valeur/qtes', 'Impairment']
    for col in cols_to_numeric:
        # The Issue Quantiti column is negative, so we take its absolute value
        if col == 'Issue Quantiti':
            combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce').abs().fillna(0)
        else:
            combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce').fillna(0)

    # STEP 3: Apply the hypothesized formula to every row
    print("\nSTEP 2: Applying the hypothesized formula to the data...")
    
    # Formula: Quantity to Impair = MAX(0, Opening Stock - Issues)
    combined_df['script_qty_to_impair'] = (combined_df['Opening Stock'] - combined_df['Issue Quantiti']).clip(lower=0)
    
    # Formula: Calculated Impairment = Quantity to Impair * Average Price
    combined_df['script_calculated_impairment'] = combined_df['script_qty_to_impair'] * combined_df['Valeur/qtes']
    
    # STEP 4: Compare our result with the Excel file's result
    print("\nSTEP 3: Comparing the script's calculation with the Excel file's 'Impairment' column...")
    
    # Calculate the difference, allowing for small floating-point rounding errors
    combined_df['difference'] = combined_df['script_calculated_impairment'] - combined_df['Impairment']
    
    # Identify how many rows have a significant mismatch (more than €1 difference)
    mismatched_rows = combined_df[combined_df['difference'].abs() > 1]
    
    # --- Final Verdict ---
    print("\n--- VERIFICATION RESULTS ---")
    if mismatched_rows.empty:
        print("✅✅✅ HYPOTHESIS CONFIRMED! The calculation logic is a perfect match.")
        print("   The correct formula is: Impairment = MAX(0, Opening Stock - Issues) * Avg Price")
    else:
        print(f"❌ HYPOTHESIS FAILED. Found {len(mismatched_rows)} records where the logic does not match.")
        print("   This indicates a more complex rule is being used in Excel.")
        print("\n--- Top 10 Mismatched Records for Analysis ---")
        print(mismatched_rows[[
            'Plant', 'Material', 'Opening Stock', 'Issue Quantiti', 'Valeur/qtes', 
            'Impairment', 'script_calculated_impairment', 'difference'
        ]].head(10).to_string())

if __name__ == "__main__":
    main()
