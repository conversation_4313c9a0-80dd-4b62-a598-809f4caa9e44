import pandas as pd
import openpyxl

# Load the final report
report_df = pd.read_csv('German_PBI_Transactional_Report_2025-07-31_FINAL_V2.csv')

# Calculate total impairment and actuals from the report
total_impairment_report = report_df['Value Assigned'].sum()
total_actuals_report = report_df['Value moved'].sum()

# Now, let's get the values from the source of truth
source_of_truth_file = r'C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\GermanyImpairmentRevamp\DE Temp POCM Calculation P07 2025_TL.xlsx'
workbook = openpyxl.load_workbook(source_of_truth_file, data_only=True)

# Get Actuals from Summary Sheet
summary_sheet = workbook['Summary']
total_actuals_sot = summary_sheet['E6'].value

# Get Impairment from Summary Imp sheet
summary_imp_sheet = workbook['Summary Imp']
impairment1 = summary_imp_sheet['B18'].value
impairment2 = summary_imp_sheet['F17'].value
total_impairment_sot = impairment1 + impairment2

# Get Actuals from Summary Imp sheet
actuals1 = summary_imp_sheet['C18'].value
actuals2 = summary_imp_sheet['G17'].value
total_actuals_sot_imp = actuals1 + actuals2


# Final Comparison
print("------ Final Validation ------")
print(f"Total Impairment from Report: {total_impairment_report:,.2f}")
print(f"Total Impairment from Source of Truth: {total_impairment_sot:,.2f}")
print(f"Impairment values match: {abs(total_impairment_report - total_impairment_sot) < 0.01}")
print("-" * 30)
print(f"Total Actuals from Report: {total_actuals_report:,.2f}")
print(f"Total Actuals from Source of Truth (Summary): {total_actuals_sot:,.2f}")
print(f"Total Actuals from Source of Truth (Summary Imp): {total_actuals_sot_imp:,.2f}")
print(f"Actuals values match (Summary): {abs(total_actuals_report - total_actuals_sot) < 0.01}")
print(f"Actuals values match (Summary Imp): {abs(total_actuals_report - total_actuals_sot_imp) < 0.01}")