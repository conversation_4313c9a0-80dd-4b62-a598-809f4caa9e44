import pandas as pd
from datetime import date, datetime
import logging
from databricks import sql
import numpy as np

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establishes and returns a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        # It is recommended to use a secure way to store and access your token
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(server_hostname=server_hostname, http_path=http_path, access_token=access_token)
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_databricks_query(query: str, connection) -> pd.DataFrame:
    """Executes a SQL query on Databricks and returns results as a DataFrame."""
    try:
        logging.info("Executing a Databricks query...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        logging.info(f"Query successful. Fetched {len(data)} rows.")
        return pd.DataFrame(data, columns=columns)
    except Exception as e:
        logging.error(f"Query execution failed: {e}")
        raise

def get_transactional_query(report_date: str) -> str:
    """
    SQL Query for transactional data with corrected joins and calculations.
    """
    glass_start_date = '2022-08-31'
    other_start_date = '2024-11-01'
    
    return f"""
    SELECT
        Subtable.SKU, Subtable.`Material Description`, Subtable.`Storage Location`,
        Subtable.`Plant Code` AS Plant, Subtable.`Movement type`, Subtable.Assigned,
        Subtable.`Moving Price` AS Price,
        CASE
            WHEN Subtable.Per IS NULL OR Subtable.Per = 0 THEN 0
            ELSE Subtable.Assigned * Subtable.`Moving Price` / Subtable.Per
        END AS `Value moved`,
        Subtable.`Entry Date`, Subtable.CumSum_ById AS `Cumulative Qty`,
        Subtable.`Total Stock`, Subtable.`Total Stock Value`
    FROM
    (
        SELECT
            d.SKU, f.maktx AS `Material Description`, d.`Storage Location`, d.`Plant Code`, d.`Movement type`, d.`Entry Date`,
            d.Quantity, e.verpr AS `Moving Price`, e.peinh AS Per, e.lbkum AS `Total Stock`, e.salk3 as `Total Stock Value`,
            SUM(d.Quantity) OVER (PARTITION BY d.`Plant Code`, d.SKU ORDER BY d.`Entry Date` DESC, d.`Movement type`) as CumSum_ById,
            CASE
                WHEN (SUM(d.Quantity) OVER (PARTITION BY d.`Plant Code`, d.SKU ORDER BY d.`Entry Date` DESC, d.`Movement type`)) < e.lbkum THEN d.Quantity
                ELSE d.Quantity - ((SUM(d.Quantity) OVER (PARTITION BY d.`Plant Code`, d.SKU ORDER BY d.`Entry Date` DESC, d.`Movement type`)) - e.lbkum)
            END AS Assigned
        FROM
        (
            SELECT SKU, `Plant Code`, `Storage Location`, `Movement type`, `Entry Date`, SUM(c.`Quantity Movement`) AS Quantity
            FROM (
                SELECT a.matnr AS SKU, a.werks AS `Plant Code`, a.lgort as `Storage Location`, a.bwart AS `Movement type`, a.cpudt_mkpf AS `Entry Date`,
                       CASE WHEN a.shkzg = 'H' THEN -a.menge ELSE a.menge END AS `Quantity Movement`
                FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg AS a
                LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS f ON a.matnr = f.matnr AND f.spras = 'E'
                WHERE a.werks LIKE 'DE%'
                  AND a.matnr BETWEEN '000000000007500000' AND '000000000007600000'
                  AND (
                      (f.maktx LIKE '%GLASS%' AND a.cpudt_mkpf BETWEEN '{glass_start_date}' AND '{report_date}')
                      OR
                      (f.maktx NOT LIKE '%GLASS%' AND a.cpudt_mkpf BETWEEN '{other_start_date}' AND '{report_date}')
                  )
            ) AS c
            WHERE `Quantity Movement` > 0
            GROUP BY SKU, `Plant Code`, `Storage Location`, `Movement type`, `Entry Date`
        ) AS d
        INNER JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS e ON d.SKU = e.matnr AND d.`Plant Code` = e.bwkey
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS f ON d.SKU = f.matnr AND f.spras = 'E'
    ) AS Subtable
    WHERE Assigned > 0;
    """

def get_aggregated_impairment_query(report_date_str: str) -> str:
    """
    SQL Query for aggregated impairment with corrected, exact start dates.
    """
    glass_start_date = '2022-08-31'
    other_start_date = '2024-11-01'

    return f"""
    WITH MaterialCategories AS (
        SELECT matnr AS SKU, CASE WHEN maktx LIKE '%GLASS%' THEN 'Glass' ELSE 'Others' END AS Category
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt WHERE spras = 'E' GROUP BY matnr, maktx
    ),
    DateRanges AS (
        SELECT SKU, Category,
               CASE
                   WHEN Category = 'Glass' THEN DATE('{glass_start_date}')
                   ELSE DATE('{other_start_date}')
               END AS StartDate
        FROM MaterialCategories
    ),
    Movements AS (
        SELECT a.matnr AS SKU, a.werks AS Plant, a.cpudt_mkpf AS EntryDate,
               CASE WHEN a.shkzg = 'S' THEN a.menge ELSE 0 END AS ReceiptQty,
               CASE WHEN a.shkzg = 'H' THEN -a.menge ELSE 0 END AS IssueQty
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg AS a
        WHERE a.werks LIKE 'DE%'
        AND a.matnr BETWEEN '000000000007500000' AND '000000000007600000'
    ),
    AggregatedFlows AS (
        SELECT m.SKU, m.Plant,
               SUM(CASE WHEN m.EntryDate < dr.StartDate THEN m.ReceiptQty + m.IssueQty ELSE 0 END) AS OpeningStock,
               SUM(CASE WHEN m.EntryDate >= dr.StartDate AND m.EntryDate <= DATE('{report_date_str}') THEN m.IssueQty ELSE 0 END) AS TotalIssues
        FROM Movements m JOIN DateRanges dr ON m.SKU = dr.SKU
        GROUP BY m.SKU, m.Plant
    ),
    Valuation AS (
        SELECT matnr AS SKU, bwkey AS Plant, lbkum AS FinalStockQty, salk3 AS FinalStockValue
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
    )
    SELECT
        agg.Plant, agg.SKU,
        CASE
            WHEN val.FinalStockQty IS NULL OR val.FinalStockQty = 0 THEN 0
            ELSE (val.FinalStockValue / val.FinalStockQty) * GREATEST(0, agg.OpeningStock + agg.TotalIssues)
        END AS TotalImpairmentValue
    FROM AggregatedFlows agg
    INNER JOIN Valuation val ON agg.SKU = val.SKU AND agg.Plant = val.Plant;
    """

if __name__ == '__main__':
    try:
        report_date_str = '2025-07-31'
        logging.info(f"Running ENRICHED TRANSACTIONAL report for period ending: {report_date_str}")
        
        impairment_file = 'Current Stock Central - Impairment Category.xlsx'
        status_file = 'Status.xlsx'

        logging.info("Loading mapping files...")
        impairment_map_df = pd.read_excel(impairment_file)
        status_map_df = pd.read_excel(status_file)

        conn = get_databricks_connection()

        transactional_df = execute_databricks_query(get_transactional_query(report_date_str), conn)
        impairment_totals_df = execute_databricks_query(get_aggregated_impairment_query(report_date_str), conn)
        
        conn.close()

        if not transactional_df.empty:
            transactional_df['SKU'] = transactional_df['SKU'].astype(str).str.lstrip('0').astype(int)
            impairment_totals_df['SKU'] = impairment_totals_df['SKU'].astype(str).str.lstrip('0').astype(int)
            impairment_map_df['SKU'] = impairment_map_df['SKU'].astype(int)
            
            final_df = pd.merge(transactional_df, impairment_totals_df, on=['Plant', 'SKU'], how='left')
            final_df['TotalImpairmentValue'].fillna(0, inplace=True)
            
            final_df['Value moved'] = pd.to_numeric(final_df['Value moved'], errors='coerce').fillna(0)
            final_df['TotalImpairmentValue'] = pd.to_numeric(final_df['TotalImpairmentValue'], errors='coerce').fillna(0)

            final_df['TotalValueForAllocation'] = final_df.groupby(['Plant', 'SKU'])['Value moved'].transform('sum')
            
            final_df['Value Assigned'] = np.where(
                final_df['TotalValueForAllocation'] != 0,
                (final_df['Value moved'] / final_df['TotalValueForAllocation']) * final_df['TotalImpairmentValue'],
                0.0
            )

            final_df = pd.merge(final_df, impairment_map_df[['SKU', 'Impairment Category']], on='SKU', how='left')
            final_df['Impairment Category'].fillna('OTHER', inplace=True)

            # *** FIX #1: Set 'Today' to be the report date for consistent age calculation ***
            final_df['Today'] = pd.to_datetime(report_date_str)
            final_df['Entry Date'] = pd.to_datetime(final_df['Entry Date'])
            
            final_df['Months'] = (final_df['Today'].dt.year - final_df['Entry Date'].dt.year) * 12 + \
                               (final_df['Today'].dt.month - final_df['Entry Date'].dt.month)
            
            final_df = pd.merge(final_df, status_map_df, on=['Impairment Category', 'Months'], how='left')
            final_df['Week_Num'] = final_df['Entry Date'].dt.isocalendar().week.apply(lambda x: f'W{x}')

            final_df = final_df.rename(columns={
                'Assigned': 'Quantity moved',
                'Entry Date': 'Entry date',
                'Total Stock Value': 'Total Value'
            })
            final_df['Country'] = 'DE'
            
            target_columns = [
                'Country', 'SKU', 'Material Description', 'Storage Location', 'Movement type', 'Quantity moved',
                'Plant', 'Price', 'Value moved', 'Entry date', 'Cumulative Qty', 'Today', 'Months', 
                'Total Stock', 'Total Value', 'Value Assigned', 'Impairment Category', 'Status', 'Week_Num'
            ]
            final_df['Assigned'] = final_df['Quantity moved']

            final_df = final_df.reindex(columns=target_columns)

            output_filename = f'German_PBI_Transactional_Report_{report_date_str}_FINAL.csv'
            # *** FIX #2: Corrected the date format typo from %ym to %Y-%m-%d ***
            final_df.to_csv(output_filename, index=False, date_format='%Y-%m-%d')
            logging.info(f"Successfully created the final report: {output_filename}")
        else:
            logging.warning("No transactional data was returned.")

    except FileNotFoundError as e:
        logging.error(f"CRITICAL ERROR: A mapping file was not found. Please ensure '{e.filename}' is in the same directory.")
    except Exception as e:
        logging.error(f"A critical error occurred: {e}")