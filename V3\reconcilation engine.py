import os
import pandas as pd
from databricks import sql
from dotenv import load_dotenv
from datetime import datetime
from dateutil.relativedelta import relativedelta
import numpy as np

# --- Configuration ---
SNAPSHOT_DATE = '2025-08-31'
CATEGORY_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Category 2.csv'
MB5L_FILE = 'MB5L.xlsx - Sheet1.csv' # Defines the scope of materials to analyze
MANUAL_ADJUSTMENT_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Base.csv'
OUTPUT_FILE = 'full_impairment_reconciliation.csv'

# Files containing the pre-calculated impairment values to validate against
VALIDATION_FILES = [
    'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 09.csv',
    'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 36.csv'
]

# This is our data-driven exclusion list, confirmed from previous analysis
EXCLUDED_KEYWORDS = [
    'BEER DISPENSE', 'COOLER', 'FURNITURE', 'EVENT MATERIAL', 'TECHNICAL MATERIAL', 
    'LEIHGUT', 'MOBILE BAR', 'PARASOLS', 'FRIDGES', 'DISPENSE COOLER', 'PERFECT DRAFT'
]

# Column names for parsing the MB5B files
MB5B_COLUMN_NAMES = [
    'Conc', 'Plant', 'Material', 'Material Description', 'Opening Stock', 'Total Receipt Qties', 
    'Issue Quantiti', 'Closing Stock', 'QTES', 'CTRL ecart', 'Valeur', 'Valeur/qtes', 
    'Opening - issue quantity >0', 'Impairment', 'NBV', 'Category'
]

load_dotenv()

def get_databricks_connection():
    """Establishes a connection to the Databricks SQL warehouse."""
    try:
        connection = sql.connect(
            server_hostname=os.getenv("DATABRICKS_HOST"),
            http_path=os.getenv("DATABRICKS_HTTP_PATH"),
            access_token=os.getenv("DATABRICKS_TOKEN")
        )
        print("✅ Successfully connected to Databricks.")
        return connection
    except Exception as e:
        print(f"❌ Failed to connect to Databricks: {e}")
        return None

def load_material_details(filepath):
    """Loads material categories and group descriptions from the category file."""
    try:
        df = pd.read_csv(filepath, usecols=['Material', 'Category', 'Material Group Desc.'], dtype={'Material': str})
        df.rename(columns={'Material Group Desc.': 'material_group'}, inplace=True)
        df['category'] = df['Category'].apply(lambda x: 'Glass & Cups' if x == 'VERRES' else 'Others')
        df['material_group'] = df['material_group'].astype(str).str.upper()
        df = df[['Material', 'category', 'material_group']].drop_duplicates('Material')
        print(f"✅ Categories and Groups loaded for {len(df)} unique materials.")
        return df
    except Exception as e:
        print(f"❌ ERROR: Could not process category file: {e}")
        return None

def load_manual_adjustments(filepath):
    """Loads manual adjustments from the Base file."""
    print("--- Loading Manual Adjustments ---")
    try:
        # We need to find the plant for each material. Let's assume it can be inferred or is not needed for this mapping
        # For this example, let's assume the base file has Material and the adjustment value
        # A more robust solution might require plant information if adjustments are plant-specific
        df = pd.read_csv(filepath, usecols=['Material', 'POCM MANUAL ADJUSTMENT'], dtype={'Material': str})
        df.rename(columns={'POCM MANUAL ADJUSTMENT': 'manual_adjustment'}, inplace=True)
        df['manual_adjustment'] = pd.to_numeric(df['manual_adjustment'], errors='coerce').fillna(0)
        # Since plant is missing, we may need to aggregate or handle this.
        # For now, we will just take the first adjustment found for a material.
        # A better approach would be to have Plant in the base file.
        # Let's assume for now that we can map without Plant.
        df = df.groupby('Material')['manual_adjustment'].sum().reset_index()
        print(f"✅ Manual adjustments loaded for {len(df)} materials.")
        return df
    except Exception as e:
        print(f"❌ ERROR: Could not process manual adjustment file '{filepath}': {e}")
        return None

def fetch_databricks_data(material_list_df, snapshot_date):
    """
    Fetches historical stock balances and all movements based on Posting Date (BUDAT).
    """
    material_tuple_list = [f"('{row['ValA']}', '{str(row['Material']).zfill(18)}')" for _, row in material_list_df.iterrows()]
    if not material_tuple_list: return None, None
    values_clause = ", ".join(material_tuple_list)

    snapshot_dt = datetime.strptime(snapshot_date, '%Y-%m-%d')
    thirty_six_months_ago = (snapshot_dt - relativedelta(months=36)).strftime('%Y-%m-%d')

    # --- CORRECTED SQL ---
    # The query now joins with MKPF to filter by BUDAT (Posting Date)
    query = f"""
    WITH SubsequentMovements AS (
        SELECT mseg.matnr, mseg.werks,
            SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS net_quantity_change,
            SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.dmbtr ELSE -mseg.dmbtr END) AS net_value_change
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
        WHERE mkpf.budat_mkpf > '{snapshot_date}' AND (mseg.werks, mseg.matnr) IN ({values_clause})
        GROUP BY mseg.matnr, mseg.werks
    ), HistoricalBalances AS (
        SELECT mbew.bwkey AS Plant, mbew.matnr AS Material, 
               (mbew.lbkum - COALESCE(sm.net_quantity_change, 0)) AS total_stock,
               (mbew.salk3 - COALESCE(sm.net_value_change, 0)) AS total_value
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS mbew
        LEFT JOIN SubsequentMovements sm ON mbew.matnr = sm.matnr AND mbew.bwkey = sm.werks
        WHERE (mbew.bwkey, mbew.matnr) IN ({values_clause})
    )
    SELECT 'balance' AS data_type, b.Plant, b.Material, makt.maktx AS material_description, 
           b.total_stock, b.total_value,
           NULL AS posting_date, NULL AS quantity, NULL as move_type
    FROM HistoricalBalances b
    LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS makt ON b.Material = makt.matnr AND makt.spras = 'E'
    WHERE b.total_stock > 0.01
    UNION ALL
    SELECT 'movement' AS data_type, mseg.werks AS Plant, mseg.matnr AS Material, NULL AS material_description, 
           NULL AS total_stock, NULL AS total_value,
           mkpf.budat_mkpf AS posting_date, mseg.menge AS quantity, mseg.shkzg AS move_type
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
    WHERE (mseg.werks, mseg.matnr) IN ({values_clause}) AND mkpf.budat_mkpf > '{thirty_six_months_ago}' AND mkpf.budat_mkpf <= '{snapshot_date}';
    """
    connection = get_databricks_connection()
    if not connection: return None, None
    try:
        print("🚀 Fetching all balances and recent movement history from Databricks (using BUDAT)...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            df = cursor.fetchall_arrow().to_pandas()
            df['Material'] = df['Material'].str.lstrip('0')
            balances = df[df['data_type'] == 'balance'].copy()
            movements = df[df['data_type'] == 'movement'].copy()
            
            for col in ['total_stock', 'total_value']:
                if col in balances.columns: balances[col] = pd.to_numeric(balances[col], errors='coerce').fillna(0)
            if 'quantity' in movements.columns: movements['quantity'] = pd.to_numeric(movements['quantity'], errors='coerce').fillna(0)
            
            print(f"✅ Retrieved {len(balances)} items with stock and {len(movements)} movement records.")
            return balances, movements
    except Exception as e:
        print(f"❌ Error during Databricks query: {e}")
        return None, None
    finally:
        if connection: connection.close(); print("Connection to Databricks closed.")

def calculate_impairment(balances_df, movements_df, material_details):
    """
    Calculates impairment based on the correct methodology:
    Impairment = Quantity_to_Impair * (Valeur / Closing Stock)
    where Quantity_to_Impair = MAX(0, Opening Stock - Issues in Period)
    """
    print("⏳ Calculating impairment using the correct methodology...")
    snapshot_dt = datetime.strptime(SNAPSHOT_DATE, '%Y-%m-%d')
    impairment_results = []
    
    if not movements_df.empty:
        movements_df['posting_date'] = pd.to_datetime(movements_df['posting_date'])

    for _, row in balances_df.iterrows():
        material, plant = row['Material'], row['Plant']
        closing_stock = row['total_stock']
        valeur = row['total_value']
        
        impairment_value = 0.0
        
        material_info_row = material_details[material_details['Material'] == material]
        material_group = material_info_row.iloc[0]['material_group'] if not material_info_row.empty else 'UNKNOWN'
        category = material_info_row.iloc[0]['category'] if not material_info_row.empty else 'Others'
        is_excluded = any(keyword in material_group for keyword in EXCLUDED_KEYWORDS)
        
        if not is_excluded and closing_stock > 0.01:
            months_threshold = 36 if category == 'Glass & Cups' else 9
            start_date = snapshot_dt - relativedelta(months=months_threshold)
            
            item_movements = movements_df[
                (movements_df['Material'] == material) &
                (movements_df['Plant'] == plant) &
                (movements_df['posting_date'] > start_date)
            ]
            
            issues_in_period = item_movements[item_movements['move_type'] == 'H']['quantity'].sum()
            receipts_in_period = item_movements[item_movements['move_type'] == 'S']['quantity'].sum()

            opening_stock = closing_stock - receipts_in_period + issues_in_period
            
            quantity_to_impair = max(0, opening_stock - issues_in_period)
            
            if quantity_to_impair > 0 and closing_stock != 0:
                precise_unit_price = valeur / closing_stock
                impairment_value = quantity_to_impair * precise_unit_price

        impairment_results.append({'Plant': plant, 'Material': material, 'script_impairment': impairment_value})

    print("✅ Impairment calculation complete.")
    return pd.DataFrame(impairment_results)

def load_validation_impairment(filepaths):
    """Loads the pre-calculated impairment values from the MB5B files."""
    print("--- Loading validation data from MB5B files ---")
    all_dfs = []
    for file in filepaths:
        try:
            df = pd.read_csv(file, skiprows=5, header=None, names=MB5B_COLUMN_NAMES, thousands=',', dtype={'Material': str})
            df.dropna(subset=['Material', 'Plant'], inplace=True)
            all_dfs.append(df)
        except Exception as e:
            print(f"❌ ERROR: Could not process validation file '{file}': {e}")
            return None
    
    combined = pd.concat(all_dfs, ignore_index=True)
    combined['excel_impairment'] = pd.to_numeric(combined['Impairment'], errors='coerce').fillna(0)
    return combined[['Plant', 'Material', 'excel_impairment']].drop_duplicates()

def main():
    """Main function to run the full reconciliation and generate a comparison report."""
    print("--- Starting Full Impairment Reconciliation ---")
    
    material_details = load_material_details(CATEGORY_FILE)
    if material_details is None: return

    manual_adjustments = load_manual_adjustments(MANUAL_ADJUSTMENT_FILE)
    if manual_adjustments is None: return

    try:
        scope_df = pd.read_csv(MB5L_FILE)
        scope_df.columns = scope_df.columns.str.strip()
        scope_df.dropna(subset=['ValA', 'Material'], inplace=True)
        scope_df['Material'] = scope_df['Material'].astype(int).astype(str)
    except FileNotFoundError:
        print(f"❌ ERROR: Scope file not found at '{MB5L_FILE}'")
        return

    balances, movements = fetch_databricks_data(scope_df, SNAPSHOT_DATE)
    if balances is None: return
    
    script_impairment = calculate_impairment(balances, movements, material_details)
    excel_impairment = load_validation_impairment(VALIDATION_FILES)
    if excel_impairment is None: return
    
    reconciliation = pd.merge(balances, script_impairment, on=['Plant', 'Material'])
    reconciliation = pd.merge(reconciliation, excel_impairment, on=['Plant', 'Material'], how='left')
    # Merge manual adjustments
    reconciliation = pd.merge(reconciliation, manual_adjustments, on=['Material'], how='left')
    reconciliation['excel_impairment'] = reconciliation['excel_impairment'].fillna(0)
    reconciliation['manual_adjustment'] = reconciliation['manual_adjustment'].fillna(0)
    
    # The 'final' excel impairment is the systematic one + manual adjustment
    reconciliation['excel_final_impairment'] = reconciliation['excel_impairment'] + reconciliation['manual_adjustment']
    reconciliation['difference'] = reconciliation['script_impairment'] - reconciliation['excel_impairment']
    
    final_cols = ['Plant', 'Material', 'material_description', 'total_stock', 'total_value', 'script_impairment', 'excel_impairment', 'manual_adjustment', 'excel_final_impairment', 'difference']
    reconciliation = reconciliation[final_cols]
    
    reconciliation.to_csv(OUTPUT_FILE, index=False, float_format='%.2f')
    print(f"\n✅ Full reconciliation report generated: '{OUTPUT_FILE}'")
    
    # We compare the script impairment with the excel impairment *before* manual adjustments
    total_script_imp = reconciliation['script_impairment'].sum()
    total_excel_imp = reconciliation['excel_impairment'].sum()
    mismatch_count = len(reconciliation[abs(reconciliation['difference']) > 0.01])

    print("\n--- Reconciliation Summary (Systematic vs. Systematic) ---")
    print(f"  > Total Impairment Calculated by Script: {total_script_imp:,.2f}")
    print(f"  > Total Systematic Impairment from Excel: {total_excel_imp:,.2f}")
    
    if mismatch_count == 0:
        print("✅✅✅ PERFECT MATCH: The script's systematic logic now perfectly replicates the Excel systematic calculation.")
    else:
        print(f"🟡 MISMATCH: Found {mismatch_count} SKUs with differing impairment values.")
        print("   Review the output file for a line-by-line comparison.")

if __name__ == "__main__":
    main()
