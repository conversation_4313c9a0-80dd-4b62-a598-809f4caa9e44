{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ae270549", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pandasql import sqldf\n", "from datetime import date\n", "import os"]}, {"cell_type": "code", "execution_count": 2, "id": "a396547b", "metadata": {}, "outputs": [], "source": ["path = r\"Clean\"\n", "\n", "MB51_1 = pd.read_excel(os.path.join(path, \"MB51\", \"MB51.xlsx\"))\n", "#MB51_2 = pd.read_excel(os.path.join(path, \"MB51\", \"MB51_DE30.xlsx\"))\n", "#MB51_3 = pd.read_excel(os.path.join(path, \"MB51\", \"MB51_REST.xlsx\"))\n", "\n", "MB52_1 = pd.read_excel(os.path.join(path, \"MB52\", \"MB52_DE02.xlsx\"))\n", "MB52_1['Plant'] = \"DE02\"\n", "\n", "MB52_2 = pd.read_excel(os.path.join(path, \"MB52\", \"MB52_DE05.xlsx\"))\n", "MB52_2['Plant'] = \"DE05\"\n", "\n", "MB52_3 = pd.read_excel(os.path.join(path, \"MB52\", \"MB52_DE06.xlsx\"))\n", "MB52_3['Plant'] = \"DE06\"\n", "\n", "MB52_4 = pd.read_excel(os.path.join(path, \"MB52\", \"MB52_DE08.xlsx\"))\n", "MB52_4['Plant'] = \"DE08\"\n", "\n", "MB52_5 = pd.read_excel(os.path.join(path, \"MB52\", \"MB52_DE30.xlsx\"))\n", "MB52_5['Plant'] = \"DE30\"\n", "\n", "Category = pd.read_excel(r'C:\\Users\\<USER>\\OneDrive - Anheuser-Busch InBev\\Documents\\Germany Impairment\\Category\\Current Stock Central - Impairment Category.xlsx')\n", "Imp_status = pd.read_excel(r'C:\\Users\\<USER>\\OneDrive - Anheuser-Busch InBev\\Documents\\Germany Impairment\\Category\\Status.xlsx')\n"]}, {"cell_type": "code", "execution_count": 3, "id": "e69f3970", "metadata": {}, "outputs": [], "source": ["MB51_1['Entry date'] = pd.to_datetime(MB51_1['Entry date'], format='%d.%m.%Y')"]}, {"cell_type": "code", "execution_count": 4, "id": "10fe1ce9", "metadata": {}, "outputs": [], "source": ["# MB51 = sqldf(\"\"\"\n", "#     SELECT * FROM MB51_DE1\n", "#     UNION ALL\n", "#     SELECT * FROM MB51_DE2\n", "# ;\"\"\")\n", "\n", "MB51 = sqldf(\"\"\"\n", "    SELECT * FROM MB51_1            \n", ";\"\"\")\n", "\n", "MB52 = sqldf(\"\"\"\n", "    SELECT * FROM MB52_1\n", "    UNION ALL\n", "    SELECT * FROM MB52_2\n", "    UNION ALL\n", "    SELECT * FROM MB52_3\n", "    UNION ALL\n", "    SELECT * FROM MB52_4\n", "    UNION ALL\n", "    SELECT * FROM MB52_5      \n", "\n", ";\"\"\")"]}, {"cell_type": "code", "execution_count": 5, "id": "834c6ed5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(694955, 10)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SKU</th>\n", "      <th>Movement type</th>\n", "      <th>Storage Location</th>\n", "      <th>Quantity moved</th>\n", "      <th>UoM</th>\n", "      <th>Plnt</th>\n", "      <th>Entry date</th>\n", "      <th>PK</th>\n", "      <th>Check_PK</th>\n", "      <th>Check</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7500229</td>\n", "      <td>641</td>\n", "      <td>1050</td>\n", "      <td>-3240</td>\n", "      <td>PC</td>\n", "      <td>DE30</td>\n", "      <td>2019-04-05 00:00:00.000000</td>\n", "      <td>DE30-7500229</td>\n", "      <td>DE30-1050</td>\n", "      <td>DE30-1050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>7500229</td>\n", "      <td>101</td>\n", "      <td>1050</td>\n", "      <td>3240</td>\n", "      <td>PC</td>\n", "      <td>DE30</td>\n", "      <td>2019-04-05 00:00:00.000000</td>\n", "      <td>DE30-7500229</td>\n", "      <td>DE30-1050</td>\n", "      <td>DE30-1050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7500229</td>\n", "      <td>102</td>\n", "      <td>1050</td>\n", "      <td>-3240</td>\n", "      <td>PC</td>\n", "      <td>DE30</td>\n", "      <td>2019-04-04 00:00:00.000000</td>\n", "      <td>DE30-7500229</td>\n", "      <td>DE30-1050</td>\n", "      <td>DE30-1050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7500229</td>\n", "      <td>101</td>\n", "      <td>1050</td>\n", "      <td>3240</td>\n", "      <td>PC</td>\n", "      <td>DE30</td>\n", "      <td>2019-04-04 00:00:00.000000</td>\n", "      <td>DE30-7500229</td>\n", "      <td>DE30-1050</td>\n", "      <td>DE30-1050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7546949</td>\n", "      <td>601</td>\n", "      <td>1050</td>\n", "      <td>-1</td>\n", "      <td>PC</td>\n", "      <td>DE30</td>\n", "      <td>2019-07-15 00:00:00.000000</td>\n", "      <td>DE30-7546949</td>\n", "      <td>DE30-1050</td>\n", "      <td>DE30-1050</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       SKU  Movement type  Storage Location  Quantity moved UoM  Plnt  \\\n", "0  7500229            641              1050           -3240  PC  DE30   \n", "1  7500229            101              1050            3240  PC  DE30   \n", "2  7500229            102              1050           -3240  PC  DE30   \n", "3  7500229            101              1050            3240  PC  DE30   \n", "4  7546949            601              1050              -1  PC  DE30   \n", "\n", "                   Entry date            PK   Check_PK     Check   \n", "0  2019-04-05 00:00:00.000000  DE30-7500229  DE30-1050  DE30-1050  \n", "1  2019-04-05 00:00:00.000000  DE30-7500229  DE30-1050  DE30-1050  \n", "2  2019-04-04 00:00:00.000000  DE30-7500229  DE30-1050  DE30-1050  \n", "3  2019-04-04 00:00:00.000000  DE30-7500229  DE30-1050  DE30-1050  \n", "4  2019-07-15 00:00:00.000000  DE30-7546949  DE30-1050  DE30-1050  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["print(MB51.shape)\n", "MB51.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "7b96c045", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(23682, 9)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Material</th>\n", "      <th>Material Description</th>\n", "      <th>Unrestricted</th>\n", "      <th>Base Unit of Measure</th>\n", "      <th>Value Unrestricted</th>\n", "      <th><PERSON><PERSON><PERSON>cy</th>\n", "      <th>Blocked</th>\n", "      <th>Value BlockedStock</th>\n", "      <th>Plant</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3364</td>\n", "      <td>CRATE FRAN 50 BLACK+ 20 BOT NRW 50 BROWN</td>\n", "      <td>-15.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3371</td>\n", "      <td>JUP KEG 50L</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3373</td>\n", "      <td>LEFF BLON EW 4X6 0,25L PAC FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3373</td>\n", "      <td>LEFF BLON EW 4X6 0,25L PAC FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3394</td>\n", "      <td>LEFF BRUN OW 4x6 0,25L SHR FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Material                      Material Description  Unrestricted  \\\n", "0      3364  CRATE FRAN 50 BLACK+ 20 BOT NRW 50 BROWN         -15.0   \n", "1      3371                               JUP KEG 50L           0.0   \n", "2      3373             LEFF BLON EW 4X6 0,25L PAC FR           0.0   \n", "3      3373             LEFF BLON EW 4X6 0,25L PAC FR           0.0   \n", "4      3394             LEFF BRUN OW 4x6 0,25L SHR FR           0.0   \n", "\n", "  Base Unit of Measure  Value Unrestricted Currency  Blocked  \\\n", "0                   PC                 0.0      EUR        0   \n", "1                   PC                 0.0      EUR        0   \n", "2                   PC                 0.0      EUR        0   \n", "3                   PC                 0.0      EUR        0   \n", "4                   PC                 0.0      EUR        0   \n", "\n", "   Value BlockedStock Plant  \n", "0                   0  DE02  \n", "1                   0  DE02  \n", "2                   0  DE02  \n", "3                   0  DE02  \n", "4                   0  DE02  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["print(MB52.shape)\n", "MB52.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "8b85de41", "metadata": {}, "outputs": [], "source": ["MB52[\"Country\"] = \"DE11\"\n", "MB52[\"Total Stock\"] = MB52[\"Unrestricted\"] +  MB52[\"Blocked\"] \n", "MB52[\"Total Value\"] = MB52[\"Value Unrestricted\"] +  MB52[\"Value BlockedStock\"]\n", "MB52[\"PK\"] =  MB52[\"Plant\"] + \"-\" + MB52[\"Material\"].astype(str)\n", "MB52.rename(columns={\"Material\": \"SKU\", \"Base Unit of Measure\": \"UoM\" }, inplace=True)"]}, {"cell_type": "code", "execution_count": 8, "id": "47665e7a", "metadata": {}, "outputs": [], "source": ["MB52.loc[MB52[\"Total Stock\"] < 0, \"Total Stock\"] = 0\n", "MB52.loc[MB52[\"Total Value\"] < 0, \"Total Value\"] = 0"]}, {"cell_type": "code", "execution_count": 9, "id": "fa5292de", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SKU</th>\n", "      <th>Material Description</th>\n", "      <th>Unrestricted</th>\n", "      <th>UoM</th>\n", "      <th>Value Unrestricted</th>\n", "      <th><PERSON><PERSON><PERSON>cy</th>\n", "      <th>Blocked</th>\n", "      <th>Value BlockedStock</th>\n", "      <th>Plant</th>\n", "      <th>Country</th>\n", "      <th>Total Stock</th>\n", "      <th>Total Value</th>\n", "      <th>PK</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3364</td>\n", "      <td>CRATE FRAN 50 BLACK+ 20 BOT NRW 50 BROWN</td>\n", "      <td>-15.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3371</td>\n", "      <td>JUP KEG 50L</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3371</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3373</td>\n", "      <td>LEFF BLON EW 4X6 0,25L PAC FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3373</td>\n", "      <td>LEFF BLON EW 4X6 0,25L PAC FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3394</td>\n", "      <td>LEFF BRUN OW 4x6 0,25L SHR FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3394</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    SKU                      Material Description  Unrestricted UoM  \\\n", "0  3364  CRATE FRAN 50 BLACK+ 20 BOT NRW 50 BROWN         -15.0  PC   \n", "1  3371                               JUP KEG 50L           0.0  PC   \n", "2  3373             LEFF BLON EW 4X6 0,25L PAC FR           0.0  PC   \n", "3  3373             LEFF BLON EW 4X6 0,25L PAC FR           0.0  PC   \n", "4  3394             LEFF BRUN OW 4x6 0,25L SHR FR           0.0  PC   \n", "\n", "   Value Unrestricted Currency  Blocked  Value BlockedStock Plant Country  \\\n", "0                 0.0      EUR        0                   0  DE02    DE11   \n", "1                 0.0      EUR        0                   0  DE02    DE11   \n", "2                 0.0      EUR        0                   0  DE02    DE11   \n", "3                 0.0      EUR        0                   0  DE02    DE11   \n", "4                 0.0      EUR        0                   0  DE02    DE11   \n", "\n", "   Total Stock  Total Value         PK  \n", "0          0.0          0.0  DE02-3364  \n", "1          0.0          0.0  DE02-3371  \n", "2          0.0          0.0  DE02-3373  \n", "3          0.0          0.0  DE02-3373  \n", "4          0.0          0.0  DE02-3394  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["MB52.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "d19b629f", "metadata": {}, "outputs": [], "source": ["cumulative = sqldf(\"\"\"\n", "with aggr as\n", "(select SKU, [Storage Location] , [Movement type], Plnt as Plant, [Entry date], sum([Quantity moved]) as [Quantity moved]\n", "from MB51\n", "where [Quantity moved] > 0\n", "group by 1,2,3,4\n", ")\n", "                   \n", "select *, sum([Quantity moved]) over(partition by Plant, SKU order by [Entry date] desc, [Movement type]) as [Cumulative Qty]\n", "from aggr\n", "where [Quantity moved] > 0                \n", "\n", "order by Plant, [Entry date] desc\n", ";\"\"\")"]}, {"cell_type": "code", "execution_count": 11, "id": "b085f066-d7fd-4ba8-84e9-515dd1054b65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5844, 7)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SKU</th>\n", "      <th>Storage Location</th>\n", "      <th>Movement type</th>\n", "      <th>Plant</th>\n", "      <th>Entry date</th>\n", "      <th>Quantity moved</th>\n", "      <th>Cumulative Qty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5691</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>123868</td>\n", "      <td>123868</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5762</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>4659</td>\n", "      <td>4659</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5770</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>9710</td>\n", "      <td>9710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5789</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>32579</td>\n", "      <td>32579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15700</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>18333</td>\n", "      <td>18333</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     SKU  Storage Location  Movement type Plant                  Entry date  \\\n", "0   5691              1060            311  DE02  2025-08-15 00:00:00.000000   \n", "1   5762              1060            311  DE02  2025-08-15 00:00:00.000000   \n", "2   5770              1060            311  DE02  2025-08-15 00:00:00.000000   \n", "3   5789              1060            311  DE02  2025-08-15 00:00:00.000000   \n", "4  15700              1060            311  DE02  2025-08-15 00:00:00.000000   \n", "\n", "   Quantity moved  Cumulative Qty  \n", "0          123868          123868  \n", "1            4659            4659  \n", "2            9710            9710  \n", "3           32579           32579  \n", "4           18333           18333  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["print(cumulative.shape)\n", "cumulative.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "ead99f57", "metadata": {}, "outputs": [], "source": ["#today = date.today()\n", "fdate = date.today().strftime('%Y-%m-%d')\n", "cumulative = cumulative.assign(Today = fdate)\n", "cumulative['Days']=(pd.to_datetime(cumulative['Today'])-pd.to_datetime(cumulative['Entry date'])).dt.days"]}, {"cell_type": "code", "execution_count": 13, "id": "a4ecf055", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SKU</th>\n", "      <th>Storage Location</th>\n", "      <th>Movement type</th>\n", "      <th>Plant</th>\n", "      <th>Entry date</th>\n", "      <th>Quantity moved</th>\n", "      <th>Cumulative Qty</th>\n", "      <th>Today</th>\n", "      <th>Days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5691</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>123868</td>\n", "      <td>123868</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5762</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>4659</td>\n", "      <td>4659</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5770</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>9710</td>\n", "      <td>9710</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5789</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>32579</td>\n", "      <td>32579</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>15700</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>18333</td>\n", "      <td>18333</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5839</th>\n", "      <td>7549003</td>\n", "      <td>1050</td>\n", "      <td>653</td>\n", "      <td>DE30</td>\n", "      <td>2018-01-17 00:00:00.000000</td>\n", "      <td>1</td>\n", "      <td>205</td>\n", "      <td>2025-08-15</td>\n", "      <td>2767</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5840</th>\n", "      <td>7556710</td>\n", "      <td>1050</td>\n", "      <td>101</td>\n", "      <td>DE30</td>\n", "      <td>2018-01-09 00:00:00.000000</td>\n", "      <td>150</td>\n", "      <td>150</td>\n", "      <td>2025-08-15</td>\n", "      <td>2775</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5841</th>\n", "      <td>7556714</td>\n", "      <td>1050</td>\n", "      <td>101</td>\n", "      <td>DE30</td>\n", "      <td>2018-01-09 00:00:00.000000</td>\n", "      <td>30</td>\n", "      <td>33</td>\n", "      <td>2025-08-15</td>\n", "      <td>2775</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5842</th>\n", "      <td>7551926</td>\n", "      <td>1050</td>\n", "      <td>653</td>\n", "      <td>DE30</td>\n", "      <td>2018-01-02 00:00:00.000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2025-08-15</td>\n", "      <td>2782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5843</th>\n", "      <td>7551927</td>\n", "      <td>1050</td>\n", "      <td>653</td>\n", "      <td>DE30</td>\n", "      <td>2018-01-02 00:00:00.000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2025-08-15</td>\n", "      <td>2782</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5844 rows × 9 columns</p>\n", "</div>"], "text/plain": ["          SKU  Storage Location  Movement type Plant  \\\n", "0        5691              1060            311  DE02   \n", "1        5762              1060            311  DE02   \n", "2        5770              1060            311  DE02   \n", "3        5789              1060            311  DE02   \n", "4       15700              1060            311  DE02   \n", "...       ...               ...            ...   ...   \n", "5839  7549003              1050            653  DE30   \n", "5840  7556710              1050            101  DE30   \n", "5841  7556714              1050            101  DE30   \n", "5842  7551926              1050            653  DE30   \n", "5843  7551927              1050            653  DE30   \n", "\n", "                      Entry date  Quantity moved  Cumulative Qty       Today  \\\n", "0     2025-08-15 00:00:00.000000          123868          123868  2025-08-15   \n", "1     2025-08-15 00:00:00.000000            4659            4659  2025-08-15   \n", "2     2025-08-15 00:00:00.000000            9710            9710  2025-08-15   \n", "3     2025-08-15 00:00:00.000000           32579           32579  2025-08-15   \n", "4     2025-08-15 00:00:00.000000           18333           18333  2025-08-15   \n", "...                          ...             ...             ...         ...   \n", "5839  2018-01-17 00:00:00.000000               1             205  2025-08-15   \n", "5840  2018-01-09 00:00:00.000000             150             150  2025-08-15   \n", "5841  2018-01-09 00:00:00.000000              30              33  2025-08-15   \n", "5842  2018-01-02 00:00:00.000000               1               1  2025-08-15   \n", "5843  2018-01-02 00:00:00.000000               1               1  2025-08-15   \n", "\n", "      Days  \n", "0        0  \n", "1        0  \n", "2        0  \n", "3        0  \n", "4        0  \n", "...    ...  \n", "5839  2767  \n", "5840  2775  \n", "5841  2775  \n", "5842  2782  \n", "5843  2782  \n", "\n", "[5844 rows x 9 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["cumulative"]}, {"cell_type": "code", "execution_count": 14, "id": "36009f38", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SKU</th>\n", "      <th>Material Description</th>\n", "      <th>Unrestricted</th>\n", "      <th>UoM</th>\n", "      <th>Value Unrestricted</th>\n", "      <th><PERSON><PERSON><PERSON>cy</th>\n", "      <th>Blocked</th>\n", "      <th>Value BlockedStock</th>\n", "      <th>Plant</th>\n", "      <th>Country</th>\n", "      <th>Total Stock</th>\n", "      <th>Total Value</th>\n", "      <th>PK</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3364</td>\n", "      <td>CRATE FRAN 50 BLACK+ 20 BOT NRW 50 BROWN</td>\n", "      <td>-15.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3371</td>\n", "      <td>JUP KEG 50L</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3371</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3373</td>\n", "      <td>LEFF BLON EW 4X6 0,25L PAC FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3373</td>\n", "      <td>LEFF BLON EW 4X6 0,25L PAC FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3394</td>\n", "      <td>LEFF BRUN OW 4x6 0,25L SHR FR</td>\n", "      <td>0.0</td>\n", "      <td>PC</td>\n", "      <td>0.0</td>\n", "      <td>EUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>DE02</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>DE02-3394</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    SKU                      Material Description  Unrestricted UoM  \\\n", "0  3364  CRATE FRAN 50 BLACK+ 20 BOT NRW 50 BROWN         -15.0  PC   \n", "1  3371                               JUP KEG 50L           0.0  PC   \n", "2  3373             LEFF BLON EW 4X6 0,25L PAC FR           0.0  PC   \n", "3  3373             LEFF BLON EW 4X6 0,25L PAC FR           0.0  PC   \n", "4  3394             LEFF BRUN OW 4x6 0,25L SHR FR           0.0  PC   \n", "\n", "   Value Unrestricted Currency  Blocked  Value BlockedStock Plant Country  \\\n", "0                 0.0      EUR        0                   0  DE02    DE11   \n", "1                 0.0      EUR        0                   0  DE02    DE11   \n", "2                 0.0      EUR        0                   0  DE02    DE11   \n", "3                 0.0      EUR        0                   0  DE02    DE11   \n", "4                 0.0      EUR        0                   0  DE02    DE11   \n", "\n", "   Total Stock  Total Value         PK  \n", "0          0.0          0.0  DE02-3364  \n", "1          0.0          0.0  DE02-3371  \n", "2          0.0          0.0  DE02-3373  \n", "3          0.0          0.0  DE02-3373  \n", "4          0.0          0.0  DE02-3394  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["MB52.head()"]}, {"cell_type": "code", "execution_count": 15, "id": "ca777830", "metadata": {}, "outputs": [], "source": ["check1 = sqldf(\"\"\"\n", "Select\n", "              \n", "m1.[Storage Location],\n", "m1.[Movement type],\n", "m1.[Quantity moved],\n", "m1.Plant,\n", "m1.[Entry date],\n", "m1.[Cumulative Qty],\n", "m1.Today,\n", "m1.Days,\n", "               \n", "m2.Country,\n", "m2.SKU,\n", "m2.[Total Stock],\n", "m2.[Total Value], \n", "m2.Price,\n", "m2.[Material Description]\n", "\n", "from cumulative m1\n", "join (select Country,[Material Description], Plant, SKU,[Total Stock], [Total Value], [Total Value]/[Total Stock] Price from MB52) m2\n", "on m1.SKU||m1.Plant = m2.SKU||m2.Plant\n", "\n", ";\"\"\")"]}, {"cell_type": "code", "execution_count": 16, "id": "cff622b7", "metadata": {}, "outputs": [], "source": ["check2 = sqldf(\"\"\"\n", "Select\n", "\n", "SKU,\n", "[Material Description],\n", "[Storage Location],\n", "[Movement type],\n", "[Quantity moved],\n", "Plant,\n", "[Entry date],\n", "[Cumulative Qty],\n", "Today,\n", "Days,\n", "                            \n", "Country,   \n", "[Total Stock],          \n", "[Total Value], \n", "Price,\n", "\n", "case\n", "    when [Cumulative Qty]<[Total Stock] then [Quantity moved]\n", "else [Quantity moved] - ([Cumulative Qty] - [Total Stock])\n", "end as Assigned\n", "\n", "from check1\n", "\n", ";\"\"\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "90659363", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "4bbc8db7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(8447, 15)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SKU</th>\n", "      <th>Material Description</th>\n", "      <th>Storage Location</th>\n", "      <th>Movement type</th>\n", "      <th>Quantity moved</th>\n", "      <th>Plant</th>\n", "      <th>Entry date</th>\n", "      <th>Cumulative Qty</th>\n", "      <th>Today</th>\n", "      <th>Days</th>\n", "      <th>Country</th>\n", "      <th>Total Stock</th>\n", "      <th>Total Value</th>\n", "      <th>Price</th>\n", "      <th>Assigned</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5691</td>\n", "      <td>BECK RET 24 0,33L CRA</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>123868</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>123868</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>DE11</td>\n", "      <td>450.0</td>\n", "      <td>931.86</td>\n", "      <td>2.070800</td>\n", "      <td>450.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5691</td>\n", "      <td>BECK RET 24 0,33L CRA</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>123868</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>123868</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5762</td>\n", "      <td>HAAK BEC CAN 24 0,5L TRA DPG</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>4659</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>4659</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>DE11</td>\n", "      <td>6.0</td>\n", "      <td>30.14</td>\n", "      <td>5.023333</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5762</td>\n", "      <td>HAAK BEC CAN 24 0,5L TRA DPG</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>4659</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>4659</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>DE11</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5770</td>\n", "      <td>HAAK BEC KEG 30L</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>9710</td>\n", "      <td>DE02</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>9710</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>DE11</td>\n", "      <td>168.0</td>\n", "      <td>899.40</td>\n", "      <td>5.353571</td>\n", "      <td>168.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    SKU          Material Description  Storage Location  Movement type  \\\n", "0  5691         BECK RET 24 0,33L CRA              1060            311   \n", "1  5691         BECK RET 24 0,33L CRA              1060            311   \n", "2  5762  HAAK BEC CAN 24 0,5L TRA DPG              1060            311   \n", "3  5762  HAAK BEC CAN 24 0,5L TRA DPG              1060            311   \n", "4  5770              HAAK BEC KEG 30L              1060            311   \n", "\n", "   Quantity moved Plant                  Entry date  Cumulative Qty  \\\n", "0          123868  DE02  2025-08-15 00:00:00.000000          123868   \n", "1          123868  DE02  2025-08-15 00:00:00.000000          123868   \n", "2            4659  DE02  2025-08-15 00:00:00.000000            4659   \n", "3            4659  DE02  2025-08-15 00:00:00.000000            4659   \n", "4            9710  DE02  2025-08-15 00:00:00.000000            9710   \n", "\n", "        Today  Days Country  Total Stock  Total Value     Price  Assigned  \n", "0  2025-08-15     0    DE11        450.0       931.86  2.070800     450.0  \n", "1  2025-08-15     0    DE11          0.0         0.00       NaN       0.0  \n", "2  2025-08-15     0    DE11          6.0        30.14  5.023333       6.0  \n", "3  2025-08-15     0    DE11          0.0         0.00       NaN       0.0  \n", "4  2025-08-15     0    DE11        168.0       899.40  5.353571     168.0  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["print(check2.shape)\n", "check2.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "ddd4de6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20486694.79\n"]}], "source": ["print(check2[\"Total Value\"].sum())"]}, {"cell_type": "code", "execution_count": 19, "id": "fbfb423c", "metadata": {}, "outputs": [], "source": ["check3 = sqldf(\"\"\"\n", "\n", "Select\n", "               \n", "Country,\n", "check2.SKU,\n", "check2.[Material Description],\n", "[Storage Location],\n", "[Movement type],\n", "[Quantity moved],\n", "Plant,\n", "[Total Value], \n", "Price,\n", "[Quantity moved]*Price as [Value moved],\n", "[Entry date],\n", "[Cumulative Qty],\n", "Today,\n", "Days/30 as Months,\n", "[Total Stock],\n", "Assigned,\n", "[Assigned]*Price as [Value Assigned],\n", "case\n", "when c.<PERSON> is null then \"OTHER\"\n", "else c.[Impairment Category]\n", "end as [Impairment Category]\n", "\n", "from check2\n", "left join Category c\n", "on c.SKU = check2.SKU\n", "where Assigned > 0\n", "               \n", ";\"\"\")"]}, {"cell_type": "code", "execution_count": 20, "id": "1a999a82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1241, 18)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>SKU</th>\n", "      <th>Material Description</th>\n", "      <th>Storage Location</th>\n", "      <th>Movement type</th>\n", "      <th>Quantity moved</th>\n", "      <th>Plant</th>\n", "      <th>Total Value</th>\n", "      <th>Price</th>\n", "      <th>Value moved</th>\n", "      <th>Entry date</th>\n", "      <th>Cumulative Qty</th>\n", "      <th>Today</th>\n", "      <th>Months</th>\n", "      <th>Total Stock</th>\n", "      <th>Assigned</th>\n", "      <th>Value Assigned</th>\n", "      <th>Impairment Category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DE11</td>\n", "      <td>5691</td>\n", "      <td>BECK RET 24 0,33L CRA</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>123868</td>\n", "      <td>DE02</td>\n", "      <td>931.86</td>\n", "      <td>2.070800</td>\n", "      <td>256505.854400</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>123868</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>450.0</td>\n", "      <td>450.0</td>\n", "      <td>931.86</td>\n", "      <td>OTHER</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DE11</td>\n", "      <td>5762</td>\n", "      <td>HAAK BEC CAN 24 0,5L TRA DPG</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>4659</td>\n", "      <td>DE02</td>\n", "      <td>30.14</td>\n", "      <td>5.023333</td>\n", "      <td>23403.710000</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>4659</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>30.14</td>\n", "      <td>OTHER</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DE11</td>\n", "      <td>5770</td>\n", "      <td>HAAK BEC KEG 30L</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>9710</td>\n", "      <td>DE02</td>\n", "      <td>899.40</td>\n", "      <td>5.353571</td>\n", "      <td>51983.178571</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>9710</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>168.0</td>\n", "      <td>168.0</td>\n", "      <td>899.40</td>\n", "      <td>OTHER</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DE11</td>\n", "      <td>5789</td>\n", "      <td>HAAK BEC RET 24 0,33L CRA</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>32579</td>\n", "      <td>DE02</td>\n", "      <td>444.71</td>\n", "      <td>2.328325</td>\n", "      <td>75854.487382</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>32579</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>191.0</td>\n", "      <td>191.0</td>\n", "      <td>444.71</td>\n", "      <td>OTHER</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DE11</td>\n", "      <td>15700</td>\n", "      <td>PALLET EURO 1200x800MM  RET V2</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>18333</td>\n", "      <td>DE02</td>\n", "      <td>0.00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>18333</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>0.00</td>\n", "      <td>OTHER</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Country    SKU            Material Description  Storage Location  \\\n", "0    DE11   5691           BECK RET 24 0,33L CRA              1060   \n", "1    DE11   5762    HAAK BEC CAN 24 0,5L TRA DPG              1060   \n", "2    DE11   5770                HAAK BEC KEG 30L              1060   \n", "3    DE11   5789       HAAK BEC RET 24 0,33L CRA              1060   \n", "4    DE11  15700  PALLET EURO 1200x800MM  RET V2              1060   \n", "\n", "   Movement type  Quantity moved Plant  Total Value     Price    Value moved  \\\n", "0            311          123868  DE02       931.86  2.070800  256505.854400   \n", "1            311            4659  DE02        30.14  5.023333   23403.710000   \n", "2            311            9710  DE02       899.40  5.353571   51983.178571   \n", "3            311           32579  DE02       444.71  2.328325   75854.487382   \n", "4            311           18333  DE02         0.00  0.000000       0.000000   \n", "\n", "                   Entry date  Cumulative Qty       Today  Months  \\\n", "0  2025-08-15 00:00:00.000000          123868  2025-08-15       0   \n", "1  2025-08-15 00:00:00.000000            4659  2025-08-15       0   \n", "2  2025-08-15 00:00:00.000000            9710  2025-08-15       0   \n", "3  2025-08-15 00:00:00.000000           32579  2025-08-15       0   \n", "4  2025-08-15 00:00:00.000000           18333  2025-08-15       0   \n", "\n", "   Total Stock  Assigned  Value Assigned Impairment Category  \n", "0        450.0     450.0          931.86               OTHER  \n", "1          6.0       6.0           30.14               OTHER  \n", "2        168.0     168.0          899.40               OTHER  \n", "3        191.0     191.0          444.71               OTHER  \n", "4        100.0     100.0            0.00               OTHER  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["print(check3.shape)\n", "check3.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "8488e56a", "metadata": {}, "outputs": [], "source": ["check4 = sqldf(\"\"\"\n", "\n", "Select\n", "Country,\n", "check3.SKU,\n", "[Material Description],\n", "[Storage Location],\n", "[Movement type],\n", "[Quantity moved],\n", "Plant,\n", "Price,\n", "[Value moved],\n", "[Entry date],\n", "[Cumulative Qty],\n", "Today,\n", "check3.Months,\n", "[Total Stock],\n", "[Total Value], \n", "Assigned,\n", "[Value Assigned],\n", "check3.[Impairment Category],\n", "case \n", "when s.Status is Null then \"Obsolete\"\n", "else s.Status\n", "end as Status,\n", "'Yes' as Mapped\n", "\n", "from check3\n", "left join Imp_status s\n", "on s.[Impairment Category] = check3.[Impairment Category]\n", "and s.Months = check3.Months\n", "where Assigned > 0\n", "               \n", ";\"\"\")"]}, {"cell_type": "code", "execution_count": 22, "id": "ee71283c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1241, 20)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>SKU</th>\n", "      <th>Material Description</th>\n", "      <th>Storage Location</th>\n", "      <th>Movement type</th>\n", "      <th>Quantity moved</th>\n", "      <th>Plant</th>\n", "      <th>Price</th>\n", "      <th>Value moved</th>\n", "      <th>Entry date</th>\n", "      <th>Cumulative Qty</th>\n", "      <th>Today</th>\n", "      <th>Months</th>\n", "      <th>Total Stock</th>\n", "      <th>Total Value</th>\n", "      <th>Assigned</th>\n", "      <th>Value Assigned</th>\n", "      <th>Impairment Category</th>\n", "      <th>Status</th>\n", "      <th>Mapped</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DE11</td>\n", "      <td>5691</td>\n", "      <td>BECK RET 24 0,33L CRA</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>123868</td>\n", "      <td>DE02</td>\n", "      <td>2.070800</td>\n", "      <td>256505.854400</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>123868</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>450.0</td>\n", "      <td>931.86</td>\n", "      <td>450.0</td>\n", "      <td>931.86</td>\n", "      <td>OTHER</td>\n", "      <td>Fast Mover</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DE11</td>\n", "      <td>5762</td>\n", "      <td>HAAK BEC CAN 24 0,5L TRA DPG</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>4659</td>\n", "      <td>DE02</td>\n", "      <td>5.023333</td>\n", "      <td>23403.710000</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>4659</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>6.0</td>\n", "      <td>30.14</td>\n", "      <td>6.0</td>\n", "      <td>30.14</td>\n", "      <td>OTHER</td>\n", "      <td>Fast Mover</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DE11</td>\n", "      <td>5770</td>\n", "      <td>HAAK BEC KEG 30L</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>9710</td>\n", "      <td>DE02</td>\n", "      <td>5.353571</td>\n", "      <td>51983.178571</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>9710</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>168.0</td>\n", "      <td>899.40</td>\n", "      <td>168.0</td>\n", "      <td>899.40</td>\n", "      <td>OTHER</td>\n", "      <td>Fast Mover</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DE11</td>\n", "      <td>5789</td>\n", "      <td>HAAK BEC RET 24 0,33L CRA</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>32579</td>\n", "      <td>DE02</td>\n", "      <td>2.328325</td>\n", "      <td>75854.487382</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>32579</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>191.0</td>\n", "      <td>444.71</td>\n", "      <td>191.0</td>\n", "      <td>444.71</td>\n", "      <td>OTHER</td>\n", "      <td>Fast Mover</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DE11</td>\n", "      <td>15700</td>\n", "      <td>PALLET EURO 1200x800MM  RET V2</td>\n", "      <td>1060</td>\n", "      <td>311</td>\n", "      <td>18333</td>\n", "      <td>DE02</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2025-08-15 00:00:00.000000</td>\n", "      <td>18333</td>\n", "      <td>2025-08-15</td>\n", "      <td>0</td>\n", "      <td>100.0</td>\n", "      <td>0.00</td>\n", "      <td>100.0</td>\n", "      <td>0.00</td>\n", "      <td>OTHER</td>\n", "      <td>Fast Mover</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Country    SKU            Material Description  Storage Location  \\\n", "0    DE11   5691           BECK RET 24 0,33L CRA              1060   \n", "1    DE11   5762    HAAK BEC CAN 24 0,5L TRA DPG              1060   \n", "2    DE11   5770                HAAK BEC KEG 30L              1060   \n", "3    DE11   5789       HAAK BEC RET 24 0,33L CRA              1060   \n", "4    DE11  15700  PALLET EURO 1200x800MM  RET V2              1060   \n", "\n", "   Movement type  Quantity moved Plant     Price    Value moved  \\\n", "0            311          123868  DE02  2.070800  256505.854400   \n", "1            311            4659  DE02  5.023333   23403.710000   \n", "2            311            9710  DE02  5.353571   51983.178571   \n", "3            311           32579  DE02  2.328325   75854.487382   \n", "4            311           18333  DE02  0.000000       0.000000   \n", "\n", "                   Entry date  Cumulative Qty       Today  Months  \\\n", "0  2025-08-15 00:00:00.000000          123868  2025-08-15       0   \n", "1  2025-08-15 00:00:00.000000            4659  2025-08-15       0   \n", "2  2025-08-15 00:00:00.000000            9710  2025-08-15       0   \n", "3  2025-08-15 00:00:00.000000           32579  2025-08-15       0   \n", "4  2025-08-15 00:00:00.000000           18333  2025-08-15       0   \n", "\n", "   Total Stock  Total Value  Assigned  Value Assigned Impairment Category  \\\n", "0        450.0       931.86     450.0          931.86               OTHER   \n", "1          6.0        30.14       6.0           30.14               OTHER   \n", "2        168.0       899.40     168.0          899.40               OTHER   \n", "3        191.0       444.71     191.0          444.71               OTHER   \n", "4        100.0         0.00     100.0            0.00               OTHER   \n", "\n", "       Status Mapped  \n", "0  Fast Mover    Yes  \n", "1  Fast Mover    Yes  \n", "2  Fast Mover    Yes  \n", "3  Fast Mover    Yes  \n", "4  Fast Mover    Yes  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["print(check4.shape)\n", "check4.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "39322c5b", "metadata": {}, "outputs": [], "source": ["PowerBI_upload = sqldf(\"\"\"\n", "select \n", "Country,\n", "SKU,\n", "[Material Description],\n", "[Storage Location],\n", "[Movement type],\n", "[Quantity moved],\n", "Plant,\n", "Price,\n", "[Value moved],\n", "[Entry date],\n", "[Cumulative Qty],\n", "Today,\n", "Months,\n", "[Total Stock],\n", "[Total Value], \n", "Assigned,\n", "[Value Assigned],\n", "[Impairment Category],\n", "case \n", "when Status is Null then \"Obsolete\"\n", "else Status\n", "end as Status\n", "\n", "from check4\n", ";\"\"\")"]}, {"cell_type": "code", "execution_count": 24, "id": "35ab2bbf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Appended 1241 new rows to existing file. Total rows: 6044\n"]}], "source": ["# Add Week_Number column with current ISO week number\n", "import datetime\n", "current_week = datetime.date.today().isocalendar()[1]\n", "PowerBI_upload['Week_Num'] = f'W{current_week:02d}'\n", "\n", "# File paths\n", "dated_file_path = r'C:\\Users\\<USER>\\OneDrive - Anheuser-Busch InBev\\Documents\\Germany Impairment\\PBI\\PBI_Output_' + fdate + '.xlsx'\n", "main_file_path = r'C:\\Users\\<USER>\\OneDrive - Anheuser-Busch InBev\\Documents\\Germany Impairment\\PBI\\PBI_Output.xlsx'\n", "\n", "# Save dated file (always create new)\n", "PowerBI_upload.to_excel(dated_file_path, sheet_name='PowerBI_upload', index=False)\n", "\n", "# For the main file, check if it exists and append if it does\n", "if os.path.exists(main_file_path):\n", "    # Read existing data\n", "    existing_data = pd.read_excel(main_file_path, sheet_name='PowerBI_upload')\n", "    \n", "    # Append new data\n", "    combined_data = pd.concat([existing_data, PowerBI_upload], ignore_index=True)\n", "    \n", "    # Save combined data\n", "    combined_data.to_excel(main_file_path, sheet_name='PowerBI_upload', index=False)\n", "    print(f\"Appended {len(PowerBI_upload)} new rows to existing file. Total rows: {len(combined_data)}\")\n", "else:\n", "    # If file doesn't exist, create it\n", "    PowerBI_upload.to_excel(main_file_path, sheet_name='PowerBI_upload', index=False)\n", "    print(f\"Created new file with {len(PowerBI_upload)} rows\")"]}, {"cell_type": "code", "execution_count": null, "id": "aef8efe5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}