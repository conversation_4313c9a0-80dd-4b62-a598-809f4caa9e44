import os
from databricks import sql
import pandas as pd

HOST, PATH, TOKEN = (os.getenv('DATABRICKS_HOST'), os.getenv('DATABRICKS_HTTP_PATH'), os.getenv('DATABRICKS_TOKEN'))
assert HOST and PATH and TOKEN

query = '''
SELECT budat_mkpf AS post_date,
       bwart,
       menge,
       shkzg,
       meins
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
WHERE matnr = 7602171
  AND werks = 'DE48'
  AND budat_mkpf <= DATE('2025-07-01')
ORDER BY post_date
'''

with sql.connect(server_hostname=HOST, http_path=PATH, access_token=TOKEN) as conn:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()

print(df.tail(40))


