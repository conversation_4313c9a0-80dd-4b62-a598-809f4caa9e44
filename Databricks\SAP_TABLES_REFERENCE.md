# SAP ECC Inventory Tables on Databricks

| Table | Key columns | What it contains | Typical filters |
|-------|-------------|------------------|-----------------|
| `brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg` | `matnr` material, `werks` plant, `bwart` movement type, `cpudt_mkpf` entry/creation date, `menge` quantity, `shkzg` debit/credit indicator | Every goods-movement line (MB51).  Positive (`shkzg='S'`) vs negative (`'H'`) quantities.  Used to reconstruct on-hand stock over time. | `matnr = ?`, `bukrs IN (…)`, `cpudt_mkpf <= 'YYYY-MM-DD'` |
| `brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew` | `matnr`, `bwkey` valuation area/plant, `verpr` moving price, `peinh` price unit divisor, `lbkum` current stock, `salk3` current stock value | Current **quantity & valuation** (what MB5B / MB5L show for an *open* posting period). | `matnr = ? AND bwkey IN (…)` |
| `brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbewh` | same keys as MBEW + `lfgja` fiscal year, `lfmon` fiscal period | Month-end history (MB5B closed periods, MB5L “Balance per” column).  Use for dates where the fiscal period is already closed. | `matnr=? AND lfgja='2025' AND lfmon='07'` |

## How to calculate stock value as of a given date

### A. Date is the **current** (open) posting period
Use `MBEW` directly—no movement reconstruction needed.
```sql
SELECT bwkey            AS plant,
       lbkum            AS qty,
       verpr, peinh,
       ROUND(lbkum * verpr / peinh, 2) AS stock_value
FROM   …mbew
WHERE  matnr = 7602171
  AND  lbkum <> 0;
```

### B. Date is a **closed** month-end (e.g. 2025-06-30)
Query that month in `MBEWH` instead of `MBEW`:
```sql
SELECT lbkum, verpr, peinh …
FROM   …mbewh
WHERE  matnr = 7602171 AND bwkey='DE48' AND lfgja=2025 AND lfmon=6;
```

### C. Date **inside** an open period but **not today** (e.g. mid-month)
1. Start with the previous closed period’s `MBEWH.lbkum`.
2. Add/subtract subsequent `MSEG` movements up to the cutoff date.
3. Multiply final qty by `verpr / peinh` from `MBEW` (price rarely changes intra-month).

The helper script `sap_stock_snapshot.py` defaults to path A.  Swap the query to `MBEWH` for path B or extend it with MSEG logic for path C.
