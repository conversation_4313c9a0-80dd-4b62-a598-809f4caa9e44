import pandas as pd
from datetime import date
import logging
from databricks import sql

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establishes and returns a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        # It is recommended to use a secure way to store and access your token
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_databricks_query(query: str, connection=None) -> pd.DataFrame:
    """Executes a SQL query on Databricks and returns results as a DataFrame."""
    close_conn = False
    if connection is None:
        connection = get_databricks_connection()
        close_conn = True
    try:
        logging.info("Executing the aggregated financial query...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        logging.info(f"Query successful. Fetched {len(data)} rows.")
        return pd.DataFrame(data, columns=columns)
    finally:
        if close_conn and connection:
            connection.close()
            logging.info("Databricks connection closed.")

def get_aggregated_impairment_query(report_date_str: str) -> str:
    """
    Generates the SQL query to replicate the aggregated impairment calculation
    from the user's Excel workbook.
    """
    return f"""
    WITH MaterialCategories AS (
        -- Define the category for each material based on the logic from the Excel file.
        -- This uses the makt table, which is more reliable than a separate mapping file.
        SELECT
            matnr AS SKU,
            maktx AS MaterialDescription,
            CASE
                WHEN maktx LIKE '%GLASS%' THEN 'Glass'
                ELSE 'Others'
            END AS Category
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt
        WHERE spras = 'E'
    ),
    DateRanges AS (
        -- Define the start and end dates for each category based on the report date.
        SELECT
            SKU,
            MaterialDescription,
            Category,
            CASE
                WHEN Category = 'Glass' THEN DATE_ADD(DATE('{report_date_str}'), -1095) -- Approx 36 months
                ELSE DATE_ADD(DATE('{report_date_str}'), -270) -- Approx 9 months
            END AS StartDate,
            DATE('{report_date_str}') AS EndDate
        FROM MaterialCategories
    ),
    Movements AS (
        -- Get all movements for German plants within the overall date range.
        SELECT
            a.matnr AS SKU,
            a.werks AS Plant,
            a.cpudt_mkpf AS EntryDate,
            CASE WHEN a.shkzg = 'S' THEN a.menge ELSE 0 END AS ReceiptQty,
            CASE WHEN a.shkzg = 'H' THEN -a.menge ELSE 0 END AS IssueQty,
            CASE WHEN a.shkzg = 'S' THEN a.menge * e.verpr / e.peinh ELSE -a.menge * e.verpr / e.peinh END AS MovementValue
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg AS a
        JOIN DateRanges dr ON a.matnr = dr.SKU
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew e ON a.matnr = e.matnr AND a.werks = e.bwkey
        WHERE a.werks LIKE 'DE%'
    ),
    AggregatedFlows AS (
        -- Aggregate movements to get Opening Stock, Receipts, and Issues for the period.
        SELECT
            m.SKU,
            dr.MaterialDescription,
            m.Plant,
            dr.Category,
            -- Opening Stock is the sum of all movements *before* the period's StartDate
            SUM(CASE WHEN m.EntryDate < dr.StartDate THEN m.ReceiptQty + m.IssueQty ELSE 0 END) AS OpeningStock,
            -- Receipts are 'S' movements *within* the period
            SUM(CASE WHEN m.EntryDate >= dr.StartDate THEN m.ReceiptQty ELSE 0 END) AS TotalReceipts,
            -- Issues are 'H' movements *within* the period
            SUM(CASE WHEN m.EntryDate >= dr.StartDate THEN m.IssueQty ELSE 0 END) AS TotalIssues
        FROM Movements m
        JOIN DateRanges dr ON m.SKU = dr.SKU
        GROUP BY m.SKU, dr.MaterialDescription, m.Plant, dr.Category
    ),
    Valuation AS (
        -- Get the latest valuation data for each material/plant
        SELECT
            matnr AS SKU,
            bwkey AS Plant,
            verpr AS MovingPrice,
            peinh AS Per,
            lbkum AS FinalStockQty,
            salk3 AS FinalStockValue
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
    )
    -- Final calculation to match the Excel logic
    SELECT
        agg.Plant,
        agg.SKU,
        agg.MaterialDescription,
        agg.OpeningStock,
        agg.TotalReceipts,
        agg.TotalIssues,
        (agg.OpeningStock + agg.TotalReceipts + agg.TotalIssues) AS ClosingStock,
        val.FinalStockValue AS TotalValue,
        -- Calculate the "at risk" quantity from opening stock that wasn't issued
        GREATEST(0, agg.OpeningStock + agg.TotalIssues) AS QtyToImpair,
        -- Calculate impairment value based on the average price
        (val.FinalStockValue / NULLIF(val.FinalStockQty, 0)) * GREATEST(0, agg.OpeningStock + agg.TotalIssues) AS ImpairmentValue,
        -- Calculate Net Book Value
        val.FinalStockValue - ((val.FinalStockValue / NULLIF(val.FinalStockQty, 0)) * GREATEST(0, agg.OpeningStock + agg.TotalIssues)) AS NetBookValue,
        agg.Category
    FROM AggregatedFlows agg
    JOIN Valuation val ON agg.SKU = val.SKU AND agg.Plant = val.Plant
    WHERE (agg.OpeningStock + agg.TotalReceipts + agg.TotalIssues) > 0 -- Only show materials with stock
    ORDER BY agg.Plant, agg.SKU;
    """

if __name__ == '__main__':
    try:
        # Set the report date. This should match the period end date of your Excel file.
        report_date_str = '2025-07-31'
        
        logging.info(f"Running AGGREGATED impairment report for Germany for period ending: {report_date_str}")
        
        # Get and execute the new aggregated query
        aggregated_query = get_aggregated_impairment_query(report_date_str)
        final_df = execute_databricks_query(aggregated_query)

        if not final_df.empty:
            # Save the aggregated results to a new CSV file
            output_filename = f'German_POCM_Aggregated_Report_{report_date_str}.csv'
            final_df.to_csv(output_filename, index=False, date_format='%Y-%m-%d')
            logging.info(f"Successfully created the aggregated report: {output_filename}")
        else:
            logging.warning("No data was returned from the query. Check the date ranges and filters.")

    except Exception as e:
        logging.error(f"A critical error occurred: {e}")