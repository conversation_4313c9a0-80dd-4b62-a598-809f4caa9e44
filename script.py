import pandas as pd
from datetime import date
import os

def process_data_for_pbi(df):
    """
    Processes the raw data from the CSV file to match the PBI_Output format.

    Args:
        df (pandas.DataFrame): The DataFrame loaded from the CSV file.

    Returns:
        pandas.DataFrame: The processed DataFrame ready for Power BI.
    """
    if df is None or df.empty:
        print("Input DataFrame is empty. Skipping processing.")
        return pd.DataFrame() # Return an empty frame

    print("Processing data for Power BI output...")
    # Ensure date columns are in the correct format
    df['EntryDate'] = pd.to_datetime(df['EntryDate'])
    df['Today'] = pd.to_datetime(df['Today'])

    # Calculate 'Months' and 'Week_Num'
    df['Months'] = (df['Days'] / 30).astype(int)
    df['Week_Num'] = 'W' + df['Today'].dt.isocalendar().week.astype(str).str.zfill(2)

    # Add placeholder columns from the PBI output that are not in our query
    # You can enhance this with your own business logic later
    df['Impairment Category'] = 'OTHER'
    df['Status'] = 'Pending'

    # Define the exact column order as in PBI_Output.xlsx
    final_column_order = [
        'Country', 'SKU', 'Material Description', 'Storage Location', 'Movement type',
        'Quantity moved', 'Plant', 'Price', 'Value moved', 'Entry date',
        'Cumulative Qty', 'Today', 'Months', 'Total Stock', 'Total Value',
        'Assigned', 'Value Assigned', 'Impairment Category', 'Status', 'Week_Num'
    ]
    
    # Rename columns to match the PBI output exactly
    df.rename(columns={
        'MaterialDescription': 'Material Description',
        'MovementType': 'Movement type',
        'QuantityMoved': 'Quantity moved',
        'EntryDate': 'Entry date',
        'CumulativeQty': 'Cumulative Qty',
        'TotalStock': 'Total Stock',
        'TotalStockValue': 'Total Value',
        'ValueMoved': 'Value moved',
        'ValueAssigned': 'Value Assigned'
    }, inplace=True)

    # Reorder the DataFrame and select only the required columns
    # This ensures the output file has columns in the correct order
    processed_df = df.reindex(columns=final_column_order)
    print("Processing complete.")
    return processed_df


if __name__ == '__main__':
    # --- IMPORTANT ---
    # 1. Place the CSV file you exported from the SQL query in the same
    #    directory as this script.
    # 2. Update the 'input_csv_filename' variable below to match your file's name.
    input_csv_filename = 'New_Query_2025_08_20_7_51pm.csv' # <--- CHANGE THIS FILENAME

    if not os.path.exists(input_csv_filename):
        print(f"Error: The file '{input_csv_filename}' was not found.")
        print("Please make sure the CSV file is in the same folder as the script and the filename is correct.")
    else:
        # 1. Load the data from the CSV file
        print(f"Reading data from '{input_csv_filename}'...")
        try:
            raw_data_df = pd.read_csv(input_csv_filename)
            print(f"Successfully loaded {len(raw_data_df)} rows from the CSV.")

            # 2. Process the data to match the PBI format
            pbi_output_df = process_data_for_pbi(raw_data_df)

            # 3. Save the final DataFrame to an Excel file
            if not pbi_output_df.empty:
                output_filename = f'PBI_Output_{date.today().strftime("%Y-%m-%d")}.xlsx'
                pbi_output_df.to_excel(output_filename, index=False, sheet_name='PowerBI_upload')
                print(f"Successfully created the output file: {output_filename}")

        except Exception as e:
            print(f"An error occurred during file processing: {e}")

