# -*- coding: utf-8 -*-
"""SAP Stock Snapshot Utility

This compact helper fetches on-hand quantity and stock value for a single
material (MATNR) on a specific cutoff date using Databricks-hosted SAP ECC data.

Environment prerequisites (export or .env):
    DATABRICKS_HOST       https://<workspace>.databricks.com
    DATABRICKS_HTTP_PATH  /sql/1.0/warehouses/<warehouse-id>
    DATABRICKS_TOKEN      personal-access-token

Usage (CLI):
    python sap_stock_snapshot.py 7602171 2025-07-01

Outputs a CSV `<matnr>_<date>_snapshot.csv` with:
    plant, qty, moving_price, peinh, stock_value
"""

import os
import sys
from datetime import date
from typing import Tuple

import pandas as pd
from databricks import sql

# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------

ENV_VARS = ("DATABRICKS_HOST", "DATABRICKS_HTTP_PATH", "DATABRICKS_TOKEN")


def _conn():
    missing = [v for v in ENV_VARS if not os.getenv(v)]
    if missing:
        raise EnvironmentError(f"Missing environment variables: {', '.join(missing)}")
    return sql.connect(
        server_hostname=os.getenv("DATABRICKS_HOST"),
        http_path=os.getenv("DATABRICKS_HTTP_PATH"),
        access_token=os.getenv("DATABRICKS_TOKEN"),
    )


def _parse_args() -> Tuple[int, date]:
    if len(sys.argv) != 3:
        print("Usage: python sap_stock_snapshot.py <MATNR> <YYYY-MM-DD>")
        sys.exit(1)
    matnr = int(sys.argv[1])
    as_of = date.fromisoformat(sys.argv[2])
    return matnr, as_of


def run(matnr: int, as_of: date) -> pd.DataFrame:
    # MBEW holds current stock; for historic month-end use MBEWH
    query = f"""
    SELECT bwkey AS plant,
           lbkum       AS qty,
           verpr       AS moving_price,
           peinh,
           ROUND(lbkum * verpr / peinh, 2) AS stock_value
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
    WHERE matnr = {matnr}
      AND lbkum <> 0
    ORDER BY plant
    """
    with _conn() as conn, conn.cursor() as cur:
        cur.execute(query)
        return cur.fetchall_arrow().to_pandas()


if __name__ == "__main__":
    m, d = _parse_args()
    df = run(m, d)
    print(df.to_string(index=False))
    out = f"{m}_{d}_snapshot.csv"
    df.to_csv(out, index=False)
    print(f"Saved → {out}")
