import pandas as pd
import numpy as np
import logging

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def compare_impairment_results():
    """
    Loads the SOT and Script output files to perform a detailed comparison
    of impairment values at the overall, plant, and SKU levels.
    """
    try:
        # --- Step 1: Load and Prepare the Source of Truth (SOT) Data ---
        logging.info("Loading Source of Truth sheets from Excel workbook...")
        sot_file_path = 'DE Temp POCM Calculation P07 2025_TL.xlsx'
        sot_09_df = pd.read_excel(sot_file_path, sheet_name='MB5B - 09', header=4)
        sot_36_df = pd.read_excel(sot_file_path, sheet_name='MB5B - 36', header=4)

        # Clean column names
        sot_09_df.rename(columns=lambda x: x.strip(), inplace=True)
        sot_36_df.rename(columns=lambda x: x.strip(), inplace=True)

        # Combine the two SOT files
        sot_combined_df = pd.concat([sot_09_df, sot_36_df])

        # Select and rename key columns
        sot_combined_df = sot_combined_df[['Plant', 'Material', 'Impairment']].copy()
        sot_combined_df.rename(columns={'Material': 'SKU', 'Impairment': 'SOT_Impairment'}, inplace=True)
        
        # Convert to numeric, coercing errors
        sot_combined_df['SKU'] = pd.to_numeric(sot_combined_df['SKU'], errors='coerce')
        sot_combined_df['SOT_Impairment'] = pd.to_numeric(sot_combined_df['SOT_Impairment'], errors='coerce')
        
        # Group by Plant and SKU to get the final SOT impairment value
        sot_final_df = sot_combined_df.groupby(['Plant', 'SKU'])['SOT_Impairment'].sum().reset_index()

        # --- Step 2: Load and Prepare the Script's Output Data ---
        logging.info("Loading the script's output file...")
        script_output_file = 'German_PBI_Transactional_Report_2025-07-31_FINAL.csv'
        script_df = pd.read_csv(script_output_file)
        
        # Select and rename key columns
        script_df_agg = script_df[['Plant', 'SKU', 'Value Assigned']].copy()
        script_df_agg.rename(columns={'Value Assigned': 'Script_Impairment'}, inplace=True)
        
        # Group by Plant and SKU to get the final script impairment value
        script_final_df = script_df_agg.groupby(['Plant', 'SKU'])['Script_Impairment'].sum().reset_index()

        # --- Step 3: Perform the Comparison ---
        logging.info("Comparing the two datasets...")
        comparison_df = pd.merge(sot_final_df, script_final_df, on=['Plant', 'SKU'], how='outer')
        
        # Fill NaN values with 0 for comparison
        comparison_df.fillna(0, inplace=True)
        
        # Calculate the difference
        comparison_df['Difference'] = comparison_df['SOT_Impairment'] - comparison_df['Script_Impairment']

        # --- Step 4: Analyze and Report the Results ---

        # Overall Totals
        total_sot = comparison_df['SOT_Impairment'].sum()
        total_script = comparison_df['Script_Impairment'].sum()
        total_diff = total_sot - total_script

        print("\n--- Overall Impairment Comparison ---")
        print(f"Total Impairment from Source of Truth: {total_sot:,.2f}")
        print(f"Total Impairment from Corrected Script: {total_script:,.2f}")
        print(f"                               Difference: {total_diff:,.2f}")
        if abs(total_diff) < 1.0:
            print("✅ Grand totals match.")
        else:
            print("❌ Grand totals DO NOT match.")

        # Plant-Level Comparison
        plant_summary = comparison_df.groupby('Plant')[['SOT_Impairment', 'Script_Impairment', 'Difference']].sum()
        plant_summary = plant_summary[plant_summary['SOT_Impairment'] + plant_summary['Script_Impairment'] != 0]
        plant_summary = plant_summary.sort_values(by='Difference', ascending=False, key=abs)
        
        print("\n--- Impairment Comparison by Plant ---")
        print(plant_summary.to_string(formatters={
            'SOT_Impairment': '{:,.2f}'.format,
            'Script_Impairment': '{:,.2f}'.format,
            'Difference': '{:,.2f}'.format
        }))

        # SKU-Level Comparison
        sku_summary = comparison_df[abs(comparison_df['Difference']) > 0.01].sort_values(by='Difference', ascending=False, key=abs)
        
        print("\n--- Top 10 SKUs with the Largest Discrepancies ---")
        print(sku_summary.head(10).to_string(formatters={
            'SOT_Impairment': '{:,.2f}'.format,
            'Script_Impairment': '{:,.2f}'.format,
            'Difference': '{:,.2f}'.format
        }))

    except FileNotFoundError as e:
        logging.error(f"CRITICAL ERROR: A file was not found. Please ensure '{e.filename}' is in the same folder as the script.")
    except KeyError as e:
        logging.error(f"CRITICAL ERROR: A required column was not found: {e}. Please check the column names in the files.")
    except Exception as e:
        logging.error(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    compare_impairment_results()