import os
from databricks import sql
import pandas as pd
from datetime import date

MATNR = 7602171  # material number
AS_OF_DATE = date(2025, 7, 1)

HOST = os.getenv('DATABRICKS_HOST')
HTTP_PATH = os.getenv('DATABRICKS_HTTP_PATH')
TOKEN = os.getenv('DATABRICKS_TOKEN')

assert HOST and HTTP_PATH and TOKEN, 'Missing Databricks connection vars'

query = f"""
WITH stock AS (
    SELECT
        werks                               AS plant,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS qty
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = {MATNR}
      AND cpudt_mkpf <= DATE('{AS_OF_DATE}')
    GROUP BY werks
)
SELECT
    s.plant,
    s.qty,
    b.verpr                    AS moving_price,
    b.peinh,
    ROUND(s.qty * b.verpr / b.peinh, 2) AS stock_value
FROM stock s
LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew b
    ON s.plant = b.bwkey AND b.matnr = {MATNR}
ORDER BY s.plant
"""

print('Running query...')
with sql.connect(server_hostname=HOST, http_path=HTTP_PATH, access_token=TOKEN) as conn:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
        print(df)
        output = f"stock_value_{MATNR}_{AS_OF_DATE}.csv"
        df.to_csv(output, index=False)
        print(f"Saved results → {output}")


