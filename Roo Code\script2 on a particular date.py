import pandas as pd
from datetime import date
import os
import logging
from databricks import sql

# --- Configuration ---
# Configure logging to display informational messages
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """
    Establish and return a connection to the Databricks SQL warehouse.
    """
    try:
        # NOTE: Credentials are hardcoded as per the provided function.
        # For production, consider using environment variables or a secrets manager.
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        if not all([server_hostname, http_path, access_token]):
            raise ValueError("Missing one or more Databricks connection variables.")
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_databricks_query(query: str, connection=None) -> pd.DataFrame:
    """
    Execute a SQL query on Databricks and return results as a DataFrame.
    """
    close_conn = False
    if connection is None:
        connection = get_databricks_connection()
        close_conn = True
    try:
        logging.info("Executing query on Databricks...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        logging.info(f"Query successful. Fetched {len(data)} rows.")
        return pd.DataFrame(data, columns=columns)
    finally:
        if close_conn and connection:
            connection.close()
            logging.info("Databricks connection closed.")

def load_category_data():
    """
    Load impairment category data from DE Temp POCM Calculation Excel file.
    """
    try:
        # Use the DE Temp POCM Calculation file like in the Databricks notebook
        category_file_path = r'C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\GermanyImpairmentRevamp\DE Temp POCM Calculation P07 2025_TL.xlsx'
        
        if os.path.exists(category_file_path):
            logging.info("Loading category data from DE Temp POCM Calculation Excel file...")
            
            try:
                category_df = pd.read_excel(category_file_path, sheet_name='Category')
                logging.info(f"Successfully loaded data from 'Category' sheet")
            except Exception as sheet_error:
                logging.warning(f"Could not read 'Category' sheet: {sheet_error}")
                category_df = pd.read_excel(category_file_path)
                logging.info(f"Loaded data from default sheet")
            
            sku_col, category_col = None, None
            for col in category_df.columns:
                if 'sku' in str(col).lower():
                    sku_col = col
                if 'category' in str(col).lower() or 'impairment' in str(col).lower():
                    category_col = col
            
            if sku_col and category_col:
                result_df = category_df[[sku_col, category_col]].copy()
                result_df.columns = ['SKU', 'Impairment Category']
                result_df.dropna(subset=['SKU', 'Impairment Category'], inplace=True)
                result_df.drop_duplicates(subset=['SKU'], keep='first', inplace=True)
                logging.info(f"Successfully loaded {len(result_df)} category mappings")
                return result_df
            else:
                logging.warning(f"Could not find SKU and Category columns.")
                return pd.DataFrame(columns=['SKU', 'Impairment Category'])
        else:
            logging.warning(f"DE Temp POCM Calculation file not found at: {category_file_path}")
            return pd.DataFrame(columns=['SKU', 'Impairment Category'])
    except Exception as e:
        logging.error(f"Error loading category data: {e}")
        return pd.DataFrame(columns=['SKU', 'Impairment Category'])

def determine_status(category, months):
    """
    Determine status based on impairment category and months aged.
    """
    category_lower = str(category).lower()
    
    if category_lower == 'glass':
        if months >= 36: return 'Obsolete'
        if months >= 10: return 'Alarm'
        if months >= 3: return 'Slow Mover'
        return 'Fast Mover'
    else: # Default for 'others' and any other category
        if months >= 9: return 'Obsolete'
        if months >= 6: return 'Alarm'
        if months >= 3: return 'Slow Mover'
        return 'Fast Mover'

def process_data_for_pbi(df):
    """
    Processes the raw data from the SQL query to match the PBI_Output format.
    """
    if df is None or df.empty:
        logging.warning("Input DataFrame is empty. Skipping processing.")
        return pd.DataFrame()

    logging.info("Processing data for Power BI output...")
    df['EntryDate'] = pd.to_datetime(df['EntryDate'])
    df['Today'] = pd.to_datetime(df['Today'])
    df['Days'] = pd.to_numeric(df['Days'], errors='coerce')
    df['Months'] = (df['Days'] / 30).fillna(0).astype(int)
    df['Week_Num'] = 'W' + df['Today'].dt.isocalendar().week.astype(str).str.zfill(2)

    logging.info("Loading and applying impairment categories...")
    category_df = load_category_data()
    
    if not category_df.empty:
        df['SKU_normalized'] = df['SKU'].astype(str).str.zfill(15).str[-7:].astype(int).astype(str)
        category_df['SKU_normalized'] = category_df['SKU'].astype(str).astype(int).astype(str)
        
        df = df.merge(category_df[['SKU_normalized', 'Impairment Category']], 
                     on='SKU_normalized', how='left')
        df.drop('SKU_normalized', axis=1, inplace=True)
        df['Impairment Category'].fillna('OTHER', inplace=True)
    else:
        logging.warning("Category DataFrame is empty - all entries will be set to 'OTHER'")
        df['Impairment Category'] = 'OTHER'
    
    logging.info("Determining status based on aging rules...")
    df['Status'] = df.apply(lambda row: determine_status(row['Impairment Category'], row['Months']), axis=1)
    
    df.rename(columns={
        'MaterialDescription': 'Material Description',
        'StorageLocation': 'Storage Location',
        'MovementType': 'Movement type',
        'QuantityMoved': 'Quantity moved',
        'EntryDate': 'Entry date',
        'TotalStock': 'Total Stock',
        'TotalStockValue': 'Total Value',
        'ValueMoved': 'Value moved'
    }, inplace=True)

    final_column_order = [
        'Country', 'SKU', 'Material Description', 'Storage Location', 'Movement type',
        'Quantity moved', 'Plant', 'Price', 'Value moved', 'Entry date',
        'Today', 'Months', 'Total Stock', 'Total Value',
        'Impairment Category', 'Status', 'Week_Num'
    ]
    
    existing_columns = [col for col in final_column_order if col in df.columns]
    processed_df = df[existing_columns]
    
    logging.info("Data processing complete.")
    return processed_df

def get_stock_movement_query():
    """
    Returns the full, corrected SQL query for stock movements,
    filtered by the hardcoded SKU range.
    """
    return f"""
    -- Corrected SQL Query for Stock Movements
    -- This query calculates the NET daily movement and provides current stock levels
    -- for a specific, hardcoded range of SKUs.

    WITH StockMovements AS (
        -- First, calculate the net quantity for every movement
        SELECT
            matnr AS SKU,
            werks AS Plant,
            lgort AS StorageLocation,
            bwart AS MovementType,
            cpudt_mkpf AS EntryDate,
            SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS NetQuantity
        FROM
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        WHERE
            werks LIKE 'DE%'
            -- Hardcoded SKU range from the POCM report
            AND matnr BETWEEN 7546978 AND 7597095
        GROUP BY
            SKU, Plant, StorageLocation, MovementType, EntryDate
    ),

    CurrentStockAndValue AS (
        -- Second, get the current total stock and value for the filtered materials
        SELECT
            mard.matnr AS SKU,
            mard.werks AS Plant,
            makt.maktx AS MaterialDescription,
            mbew.verpr AS MovingPrice,
            mbew.peinh AS PriceUnit,
            mard.labst AS TotalStock,
            (mard.labst * (mbew.verpr / NULLIF(mbew.peinh, 0))) AS TotalStockValue
        FROM
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard AS mard
        LEFT JOIN
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS mbew
            ON mard.matnr = mbew.matnr AND mard.werks = mbew.bwkey
        LEFT JOIN
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS makt
            ON mard.matnr = makt.matnr AND makt.spras = 'E'
        WHERE
            mard.werks LIKE 'DE%'
            AND mard.matnr BETWEEN 7546978 AND 7597095
    )

    -- Finally, join the movements with the current stock and value information
    SELECT
        'DE' AS Country,
        mov.SKU,
        cs.MaterialDescription,
        mov.StorageLocation,
        mov.MovementType,
        mov.Plant, 
        mov.EntryDate,
        mov.NetQuantity AS QuantityMoved,
        cs.MovingPrice / NULLIF(cs.PriceUnit, 0) AS Price,
        mov.NetQuantity * (cs.MovingPrice / NULLIF(cs.PriceUnit, 0)) AS ValueMoved,
        CAST('2025-07-31' AS DATE) AS Today, 
-- The age calculation is now relative to the last day of Week 33
        DATEDIFF(day, mov.EntryDate, '2025-07-31') AS Days, 
        cs.TotalStock,
        cs.TotalStockValue
    FROM
        StockMovements mov
    LEFT JOIN
        CurrentStockAndValue cs
        -- <<< THIS IS THE FINAL FIX: Normalizing the SKU format to handle leading zeros
        ON TRIM(LEADING '0' FROM mov.SKU) = TRIM(LEADING '0' FROM cs.SKU) AND mov.Plant = cs.Plant
    WHERE
        mov.NetQuantity != 0
    ORDER BY
        mov.Plant, mov.StorageLocation, mov.SKU, mov.EntryDate DESC;
    """

if __name__ == '__main__':
    try:
        # Get the full SQL query with the hardcoded SKU range
        stock_query = get_stock_movement_query()

        # Execute the query on Databricks
        raw_data_df = execute_databricks_query(stock_query)

        # Process the data to match the PBI format
        if not raw_data_df.empty:
            pbi_output_df = process_data_for_pbi(raw_data_df)

            # Save the final DataFrame to a file
            output_filename = f'PBI_Output_Corrected_31st July.csv'
            pbi_output_df.to_csv(output_filename, index=False)
            logging.info(f"Successfully created the output file: {output_filename}")
        else:
            logging.warning("No data was returned from the query. The output file was not created.")

    except Exception as e:
        logging.error(f"An error occurred during the script execution: {e}")