import pandas as pd
import numpy as np

# --- Configuration ---
CALCULATED_FILE = 'impairment_summary_output.csv'
REFERENCE_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Material Level Summary.csv'

def load_and_clean_reference_data(filepath):
    """Loads and cleans the specific, non-standard format of the reference summary file."""
    try:
        # The useful data starts around row 5, with specific column names
        df = pd.read_csv(filepath, header=4) # header=4 starts reading from the 5th row
        df.columns = df.columns.str.strip()
        
        # Select and rename the relevant columns
        df = df[['Plant', 'Material', 'Material Description', 'Total']]
        df.rename(columns={
            'Plant': 'plant_code',
            'Material': 'material',
            'Total': 'reference_impairment'
        }, inplace=True)

        # The file has summary rows (like 'DE02 Total') that need to be filtered out.
        # We can do this by ensuring 'Material' can be converted to a number.
        df = df[pd.to_numeric(df['material'], errors='coerce').notna()]
        df['material'] = df['material'].astype(int).astype(str)

        # Forward-fill the plant codes, as they only appear on the first row of each group
        df['plant_code'].ffill(inplace=True) # FIX: Updated from deprecated method
        df.dropna(subset=['plant_code', 'material'], inplace=True)
        
        df['reference_impairment'] = pd.to_numeric(df['reference_impairment'], errors='coerce').fillna(0)
        
        # Group by plant and material in case there are duplicates, summing impairment
        grouped = df.groupby(['plant_code', 'material'])['reference_impairment'].sum().reset_index()
        return grouped

    except FileNotFoundError:
        print(f"❌ QC ERROR: Reference summary file not found at '{filepath}'")
        return None
    except Exception as e:
        print(f"❌ QC ERROR: Could not process reference file: {e}")
        return None

def main():
    """Main QC function to compare calculated results with the reference file."""
    print("\n--- Running Quality Control (QC) Script ---")
    
    try:
        calculated_df = pd.read_csv(CALCULATED_FILE)
        calculated_df['material'] = calculated_df['material'].astype(str)
        print(f"✅ Successfully loaded calculated results from '{CALCULATED_FILE}'")
    except FileNotFoundError:
        print(f"❌ QC ERROR: Calculated file not found. Please run 'impairment_calculation.py' first.")
        return

    reference_df = load_and_clean_reference_data(REFERENCE_FILE)
    if reference_df is None: return

    # --- Totals Comparison ---
    total_calculated_impairment = calculated_df['impairment_value'].sum()
    total_reference_impairment = reference_df['reference_impairment'].sum()

    print("\n--- Overall Totals Validation ---")
    print(f"| Metric              | Calculated Value   | Reference Value    | Match     |")
    print(f"|---------------------|--------------------|--------------------|-----------|")
    match_status = '✅ Yes' if np.isclose(total_calculated_impairment, total_reference_impairment, atol=1) else '❌ No'
    print(f"| Total Impairment    | {total_calculated_impairment:18,.2f} | {total_reference_impairment:18,.2f} | {match_status.ljust(9)} |")
    print("--------------------------------------------------------------------------")

    if match_status == '❌ No':
        # --- Detailed Discrepancy Analysis ---
        comparison_df = pd.merge(
            calculated_df[['plant_code', 'material', 'impairment_value']],
            reference_df,
            on=['plant_code', 'material'],
            how='outer'
        ).fillna(0)

        comparison_df['difference'] = comparison_df['impairment_value'] - comparison_df['reference_impairment']
        comparison_df['abs_difference'] = comparison_df['difference'].abs()

        discrepancies = comparison_df[comparison_df['abs_difference'] > 0.01].sort_values(by='abs_difference', ascending=False)

        if not discrepancies.empty:
            print("\n--- Top 10 Discrepancies Found ---")
            print(discrepancies.head(10).to_string())
        else:
            print("\n✅ Note: Totals differ slightly due to rounding, but all individual items match.")

if __name__ == "__main__":
    main()

