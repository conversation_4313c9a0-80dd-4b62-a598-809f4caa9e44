import os
import pandas as pd
from databricks import sql
import datetime

# Databricks connection parameters
def get_databricks_connection():
    """Establish connection to Databricks SQL warehouse"""
    connection = sql.connect(
        server_hostname="adb-671640162813626.6.azuredatabricks.net",
        http_path="/sql/1.0/warehouses/b22260fd910a310c",
        access_token="**************************************"
    )
    return connection

def run_inventory_movement_query():
    """
    Execute query to retrieve inventory movement data with assigned values
    from SAP ECC Europe data in Databricks
    """
    query = """
    SELECT  
        *, 
        subtable.assigned * subtable.moving_price / subtable.per_unit AS assigned_value
    FROM (
        SELECT 
            d.sku,
            f.maktx AS material_description,
            d.plant_code,
            d.movement_type,
            d.entry_date,
            d.quantity,
            SUM(d.quantity) OVER (
                PARTITION BY d.plant_code, d.sku 
                ORDER BY d.entry_date DESC, d.movement_type
            ) AS cumsum_by_id,
            d.today,
            d.days,
            e.verpr AS moving_price,
            e.peinh AS per_unit,
            e.lbkum AS total_stock,
            e.salk3 AS total_stock_value,
            CASE
                WHEN SUM(d.quantity) OVER (
                    PARTITION BY d.plant_code, d.sku 
                    ORDER BY d.entry_date DESC, d.movement_type
                ) < e.lbkum
                THEN d.quantity
                ELSE d.quantity - (
                    SUM(d.quantity) OVER (
                        PARTITION BY d.plant_code, d.sku 
                        ORDER BY d.entry_date DESC, d.movement_type
                    ) - e.lbkum
                )
            END AS assigned
        FROM (
            SELECT 
                sku,
                plant_code,
                movement_type,
                entry_date,
                SUM(c.quantity_movement) AS quantity,
                CURRENT_DATE() AS today,
                DATEDIFF(CURRENT_DATE(), entry_date) AS days
            FROM (
                SELECT 
                    a.bwart AS movement_type,
                    a.matnr AS sku,
                    a.werks AS plant_code,
                    CASE 
                        WHEN a.shkzg = 'H' THEN -1 * a.menge
                        WHEN a.shkzg = 'S' THEN 1 * a.menge
                    END AS quantity_movement,
                    a.cpudt_mkpf AS entry_date
                FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg AS a
                WHERE a.bukrs IN ('SE11', 'NO11', 'DK11', 'FI11')
                  AND a.matnr BETWEEN 7500000 AND 7600000
            ) AS c
            WHERE quantity_movement > 0
            GROUP BY sku, plant_code, movement_type, entry_date
        ) AS d
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS e
            ON d.sku = e.matnr AND d.plant_code = e.bwkey
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS f
            ON d.sku = f.matnr AND f.spras = 'E'
    ) AS subtable
    WHERE assigned > 0
    ORDER BY entry_date DESC
    """
    
    try:
        # Establish connection
        connection = get_databricks_connection()
        
        # Execute query and fetch results
        with connection.cursor() as cursor:
            cursor.execute(query)
            result = cursor.fetchall_arrow()
            
            # Convert to pandas DataFrame
            df = result.to_pandas()
            
            print(f"Retrieved {len(df)} records from Databricks")
            return df
            
    except Exception as e:
        print(f"Error executing Databricks query: {e}")
        return None
    finally:
        if connection:
            connection.close()

def main():
    """Main function to execute the query and process results"""
    print("Starting Databricks query execution...")
    df = run_inventory_movement_query()
    
    if df is not None:
        # Display basic statistics
        print("\nData Summary:")
        print(f"Total records: {len(df)}")
        print(f"Unique SKUs: {df['sku'].nunique()}")
        print(f"Unique plants: {df['plant_code'].nunique()}")
        print(f"Date range: {df['entry_date'].min()} to {df['entry_date'].max()}")
        
        # Optional: Save to CSV
        # timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # df.to_csv(f"inventory_movement_{timestamp}.csv", index=False)
        
        return df
    
if __name__ == "__main__":
    main()
