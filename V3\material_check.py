import pandas as pd
import numpy as np

# --- Configuration ---
# The file that tells us which items SHOULD be in the report
SUMMARY_FILTER_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Material Level Summary.csv'
# The files containing the raw data we are filtering through
DETAILED_FILES = [
    'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 09.csv',
    'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 36.csv'
]
# Column names for parsing the messy MB5B files
COLUMN_NAMES = [
    'Conc', 'Plant', 'Material', 'Material Description', 'Opening Stock', 'Total Receipt Qties', 
    'Issue Quantiti', 'Closing Stock', 'QTES', 'CTRL ecart', 'Valeur', 'Valeur/qtes', 
    'Opening - issue quantity >0', 'Impairment', 'NBV', 'Category'
]

def get_summary_filter_list(filepath):
    """Parses the summary report to get the list of (Plant, Material) keys."""
    try:
        df = pd.read_csv(filepath, header=None, dtype=str)
        valid_rows = pd.to_numeric(df[1], errors='coerce').notna()
        df = df[valid_rows]
        df = df[[0, 1]].rename(columns={0: 'Plant', 1: 'Material'})
        df['Plant'].replace('', np.nan, inplace=True)
        df['Plant'].ffill(inplace=True)
        df.dropna(inplace=True)
        # Create a unique key for each item
        df['filter_key'] = df['Plant'] + '_' + df['Material']
        return set(df['filter_key'])
    except Exception as e:
        print(f"❌ ERROR: Could not parse summary file '{filepath}': {e}")
        return None

def get_detailed_data_keys(filepaths):
    """Loads the detailed MB5B files to get a list of all available (Plant, Material) keys."""
    all_keys = set()
    for file in filepaths:
        try:
            df = pd.read_csv(file, skiprows=5, header=None, names=COLUMN_NAMES, thousands=',', dtype={'Material': str})
            df.dropna(subset=['Material', 'Plant'], inplace=True)
            df['filter_key'] = df['Plant'] + '_' + df['Material']
            all_keys.update(df['filter_key'])
        except Exception as e:
            print(f"❌ ERROR: Could not process file '{file}': {e}")
            return None
    return all_keys

def main():
    """Compares the two lists of materials and identifies what's missing."""
    print("--- Running Analysis to Find Missing Material ---")
    
    # Get the list of materials that are SUPPOSED to be in the report
    summary_keys = get_summary_filter_list(SUMMARY_FILTER_FILE)
    if summary_keys is None: return
    print(f"✅ Found {len(summary_keys)} unique items in the target summary report.")

    # Get the list of all materials that are AVAILABLE in our source data
    detailed_keys = get_detailed_data_keys(DETAILED_FILES)
    if detailed_keys is None: return
    print(f"✅ Found {len(detailed_keys)} unique items in the detailed source files.")
    
    # Find the difference
    missing_keys = summary_keys - detailed_keys
    
    print("\n--- Analysis Result ---")
    if not missing_keys:
        print("✅ No missing items found. All items from the summary exist in the detailed files.")
    else:
        print(f"🔴 Found {len(missing_keys)} item(s) that are in the summary report but MISSING from the detailed calculation sheets:")
        for key in missing_keys:
            print(f"  - {key}")
        print("\nThis discrepancy is the cause of the difference in totals.")

if __name__ == "__main__":
    main()
