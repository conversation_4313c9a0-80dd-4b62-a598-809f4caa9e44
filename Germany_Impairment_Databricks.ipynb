{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Germany Impairment Analysis - Databricks Integration\n", "\n", "This notebook integrates with Databricks to fetch and analyze impairment data for Germany operations."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Required Libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Core data manipulation and analysis\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# System and environment\n", "import os\n", "from dotenv import load_dotenv\n", "import traceback\n", "\n", "# Databricks connectivity\n", "from databricks import sql\n", "\n", "# Additional utilities\n", "import logging\n", "from typing import Optional, Dict, Any\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Databricks Connection Setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading environment variables from: Databricks\\.env\n", "Environment file exists: True\n"]}], "source": ["# Load environment variables from Databricks/.env file\n", "env_path = os.path.join('Databricks', '.env')\n", "load_dotenv(env_path)\n", "\n", "print(f\"Loading environment variables from: {env_path}\")\n", "print(f\"Environment file exists: {os.path.exists(env_path)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_databricks_connection():\n", "    \"\"\"\n", "    Establish and return a connection to the Databricks SQL warehouse.\n", "    \"\"\"\n", "    try:\n", "        # Get required environment variables (Note: credentials are hardcoded here)\n", "        server_hostname = \"adb-671640162813626.6.azuredatabricks.net\"\n", "        http_path = \"/sql/1.0/warehouses/b22260fd910a310c\"\n", "        access_token = \"**************************************\"\n", "        \n", "        if not all([server_hostname, http_path, access_token]):\n", "            raise ValueError(\"Missing one or more Databricks connection variables.\")\n", "        \n", "        logging.info(\"Establishing connection to Databricks...\")\n", "        connection = sql.connect(\n", "            server_hostname=server_hostname,\n", "            http_path=http_path,\n", "            access_token=access_token\n", "        )\n", "        logging.info(\"Successfully connected to Databricks SQL warehouse\")\n", "        return connection\n", "        \n", "    except Exception as e:\n", "        logging.error(f\"Failed to connect to Databricks: {e}\")\n", "        raise"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-20 19:59:14,554 - INFO - Establishing connection to Databricks...\n", "2025-08-20 19:59:17,377 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:17,378 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:17,380 - INFO - Successfully opened session 01f07dd2-0eb3-11b8-a5e9-696899419809\n", "2025-08-20 19:59:17,381 - INFO - Successfully connected to Databricks SQL warehouse\n", "2025-08-20 19:59:17,385 - INFO - Closing session 01f07dd2-0eb3-11b8-a5e9-696899419809\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Databricks connection established successfully\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-20 19:59:17,675 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:17,676 - INFO - HTTP Response with status code 200, message: OK\n"]}], "source": ["# Test the connection\n", "try:\n", "    conn = get_databricks_connection()\n", "    print(\"✅ Databricks connection established successfully\")\n", "    conn.close()\n", "except Exception as e:\n", "    print(f\"❌ Connection failed: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Fetching Functions"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def execute_databricks_query(query: str, connection=None) -> pd.DataFrame:\n", "    \"\"\"\n", "    Execute a SQL query on Databricks and return results as DataFrame.\n", "    \"\"\"\n", "    close_conn = False\n", "    if connection is None:\n", "        connection = get_databricks_connection()\n", "        close_conn = True\n", "    try:\n", "        with connection.cursor() as cursor:\n", "            cursor.execute(query)\n", "            columns = [desc[0] for desc in cursor.description]\n", "            data = cursor.fetchall()\n", "        return pd.DataFrame(data, columns=columns)\n", "    finally:\n", "        if close_conn and connection:\n", "            connection.close()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def fetch_movement_data(connection=None, **kwargs) -> pd.DataFrame:\n", "    \"\"\"\n", "    Fetch movement data from Databricks.\n", "    \"\"\"\n", "    logging.info(\"Fetching movement data from Databricks...\")\n", "    query = \"\"\"\n", "    SELECT matnr AS SKU, lgort AS `Storage Location`, bwart AS `Movement type`,\n", "           werks AS Plnt, cpudt_mkpf AS `Entry date`, menge AS `Quantity moved`,\n", "           shkzg AS `Debit/Credit Indicator`\n", "    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg\n", "    WHERE werks IN ('DE02', 'DE05', 'DE06', 'DE08', 'DE30')\n", "    ORDER BY cpudt_mkpf DESC, werks, matnr\n", "    \"\"\"\n", "    try:\n", "        df = execute_databricks_query(query, connection)\n", "        logging.info(f\"Successfully fetched {len(df)} movement records\")\n", "        return df\n", "    except Exception as e:\n", "        logging.error(f\"Error fetching movement data: {e}\")\n", "        raise"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def fetch_stock_data(connection=None, **kwargs) -> pd.DataFrame:\n", "    \"\"\"\n", "    Fetch stock data from Databricks.\n", "    \"\"\"\n", "    logging.info(\"Fetching stock data from Databricks...\")\n", "    query = \"\"\"\n", "    SELECT matnr AS SKU, bwkey AS Plant, lbkum AS `Total Stock`,\n", "           salk3 AS `Total Value`, verpr AS Price, peinh AS `Price Unit`\n", "    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew\n", "    WHERE bwkey IN ('DE02', 'DE05', 'DE06', 'DE08', 'DE30')\n", "    ORDER BY bwkey, matnr\n", "    \"\"\"\n", "    try:\n", "        df = execute_databricks_query(query, connection)\n", "        logging.info(f\"Successfully fetched {len(df)} stock records\")\n", "        return df\n", "    except Exception as e:\n", "        logging.error(f\"Error fetching stock data: {e}\")\n", "        raise"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def fetch_category_data() -> pd.DataFrame:\n", "    \"\"\"\n", "    Read category data from the local Excel file.\n", "    \"\"\"\n", "    logging.info(\"Reading category data from Excel file...\")\n", "    try:\n", "        excel_file = 'DE Temp POCM Calculation P07 2025_TL.xlsx'\n", "        category_df = pd.read_excel(excel_file, sheet_name='Category')\n", "        result_df = category_df[['SKU', 'Category']].copy()\n", "        result_df.dropna(subset=['SKU', 'Category'], inplace=True)\n", "        result_df.drop_duplicates(subset=['SKU'], keep='first', inplace=True)\n", "        logging.info(f\"Successfully loaded {len(result_df)} category mappings\")\n", "        return result_df\n", "    except Exception as e:\n", "        logging.error(f\"Error reading category data: {e}\")\n", "        raise"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def fetch_status_rules() -> pd.DataFrame:\n", "    \"\"\"\n", "    Read status rules from the local Excel file.\n", "    \"\"\"\n", "    logging.info(\"Reading status rules from Excel file...\")\n", "    try:\n", "        excel_file = 'DE Temp POCM Calculation P07 2025_TL.xlsx'\n", "        notes_df = pd.read_excel(excel_file, sheet_name='Notes')\n", "        rules_data = []\n", "        for _, row in notes_df.iterrows():\n", "            months_col, category_col = row.get('Unnamed: 11', ''), row.get('Unnamed: 12', '')\n", "            if pd.notna(months_col) and pd.notna(category_col) and 'month' in str(months_col).lower():\n", "                try:\n", "                    months_num = int(''.join(filter(str.isdigit, str(months_col))))\n", "                    if months_num > 0 and str(category_col):\n", "                        rules_data.append({'Category': str(category_col).strip(), 'Months': months_num})\n", "                except (ValueErro<PERSON>, TypeError):\n", "                    continue\n", "        if rules_data:\n", "            result_df = pd.DataFrame(rules_data).drop_duplicates(subset=['Category'], keep='first')\n", "        else:\n", "            logging.warning(\"Could not extract rules, using defaults.\")\n", "            result_df = pd.DataFrame({'Category': ['Glass', 'Others'], 'Months': [36, 9]})\n", "        logging.info(f\"Successfully loaded {len(result_df)} status rules\")\n", "        return result_df\n", "    except Exception as e:\n", "        logging.error(f\"Error reading status rules: {e}\")\n", "        raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Core Data Processing Function"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def process_and_age_stock(movement_df: pd.DataFrame, stock_df: pd.DataFrame, \n", "                         category_df: pd.DataFrame, status_rules_df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Process and age stock data, including debug and fix steps for data quality.\n", "    \"\"\"\n", "    logging.info(\"Starting stock aging process...\")\n", "    try:\n", "        # Step 1: Filter and aggregate positive stock movements\n", "        logging.info(\"Step 1: Aggregating positive stock movements\")\n", "        movement_df['Entry date'] = pd.to_datetime(movement_df['Entry date'])\n", "        movement_filtered = movement_df[movement_df['Quantity moved'] > 0].copy()\n", "        movement_agg = movement_filtered.groupby(['SKU', 'Storage Location', 'Movement type', 'Plnt', 'Entry date'])['Quantity moved'].sum().reset_index()\n", "        movement_agg.rename(columns={'Plnt': 'Plant'}, inplace=True)\n", "        \n", "        # Step 2: Calculate cumulative quantities\n", "        logging.info(\"Step 2: Preparing and calculating cumulative quantities\")\n", "        \n", "        # --- DEBUG AND FIX SECTION --- #\n", "        print(\"\\n--- Debugging 'Quantity moved' column ---\")\n", "        print(f\"Original DType: {movement_agg['Quantity moved'].dtype}\")\n", "        \n", "        def fix_trailing_minus(val):\n", "            if isinstance(val, str) and val.endswith('-'):\n", "                return '-' + val[:-1]\n", "            return val\n", "        \n", "        # Apply the fix for trailing minus signs\n", "        fixed_values = movement_agg['Quantity moved'].apply(fix_trailing_minus)\n", "        numeric_values = pd.to_numeric(fixed_values, errors='coerce')\n", "        \n", "        # Identify and report any values that still fail to convert\n", "        problematic_mask = numeric_values.isna() & movement_agg['Quantity moved'].notna()\n", "        if problematic_mask.any():\n", "            unique_problems = movement_agg.loc[problematic_mask, 'Quantity moved'].unique()\n", "            print(f\"Found {len(unique_problems)} unique problematic values that could not be converted.\")\n", "            print(f\"Examples: {unique_problems[:10]}\") # Shows up to 10 examples\n", "        else:\n", "            print(\"No data conversion issues found. All values are numeric.\")\n", "        print(\"--- End of Debug ---\\n\")\n", "        \n", "        # Apply the validated conversion\n", "        movement_agg['Quantity moved'] = numeric_values\n", "        movement_agg.dropna(subset=['Quantity moved'], inplace=True)\n", "        # --- END OF DEBUG AND FIX --- #\n", "        \n", "        movement_agg.sort_values(['Plant', 'SKU', 'Entry date'], ascending=[True, True, False], inplace=True)\n", "        movement_agg['Cumulative Qty'] = movement_agg.groupby(['Plant', 'SKU'])['Quantity moved'].cumsum()\n", "        \n", "        # Step 3: Join with current stock data\n", "        logging.info(\"Step 3: Joining with current stock data\")\n", "        stock_prepared = stock_df.copy()\n", "        stock_prepared['Country'] = 'DE11'\n", "        stock_prepared['Price'] = stock_prepared.apply(lambda r: r['Total Value'] / r['Total Stock'] if r['Total Stock'] > 0 else 0, axis=1)\n", "        joined_data = movement_agg.merge(stock_prepared, on=['SKU', 'Plant'], how='inner')\n", "        \n", "        # Step 4: Calculate assigned quantities\n", "        logging.info(\"Step 4: Calculating assigned quantities\")\n", "        def calculate_assigned(r):\n", "            return r['Quantity moved'] if r['Cumulative Qty'] < r['Total Stock'] else max(0, r['Quantity moved'] - (r['Cumulative Qty'] - r['Total Stock']))\n", "        joined_data['Assigned'] = joined_data.apply(calculate_assigned, axis=1)\n", "        assigned_data = joined_data[joined_data['Assigned'] > 0].copy()\n", "        \n", "        # Step 5 & 6: Add category, values, and status\n", "        logging.info(\"Step 5 & 6: Adding categories, values, and status\")\n", "        assigned_data['Value moved'] = assigned_data['Quantity moved'] * assigned_data['Price']\n", "        assigned_data['Today'] = pd.to_datetime('today').normalize()\n", "        assigned_data['Week_Num'] = assigned_data['Entry date'].dt.strftime('W%U')\n", "        assigned_data['Months'] = (assigned_data['Today'] - assigned_data['Entry date']).dt.days / 30.0\n", "        assigned_data['Value Assigned'] = assigned_data['Assigned'] * assigned_data['Price']\n", "        assigned_data = assigned_data.merge(category_df, on='SKU', how='left')\n", "        assigned_data['Impairment Category'] = assigned_data['Category'].fillna('OTHER')\n", "\n", "        def determine_status(r):\n", "            # Ensure case-insensitivity for category matching\n", "            category_lower = str(r['Impairment Category']).lower()\n", "            if (category_lower == 'glass' and r['Months'] >= 36) or \\\n", "               (category_lower in ['others', 'other'] and r['Months'] >= 9):\n", "                return 'Obsolete'\n", "            elif (category_lower == 'glass' and r['Months'] >= 10) or \\\n", "                 (category_lower in ['others', 'other'] and r['Months'] >= 6):\n", "                return 'Alarm'\n", "            elif (category_lower == 'glass' and r['Months'] >= 3) or \\\n", "                 (category_lower in ['others', 'other'] and r['Months'] >= 3):\n", "                return 'Slow Mover'\n", "            return 'Fast Mover'\n", "            \n", "        assigned_data['Status'] = assigned_data.apply(determine_status, axis=1)\n", "        \n", "        # This column is not in the final output but needed for calculation\n", "        assigned_data['Material Description'] = assigned_data['SKU'].astype(str) + ' - Material'\n", "\n", "\n", "        result_df = assigned_data.copy()\n", "        result_df[['Months', 'Value Assigned', 'Value moved']] = result_df[['Months', 'Value Assigned', 'Value moved']].round(2)\n", "        \n", "        logging.info(f\"Successfully processed {len(result_df)} aged stock layers\")\n", "        return result_df\n", "\n", "    except Exception as e:\n", "        logging.error(f\"Error in process_and_age_stock: {e}\")\n", "        raise\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Output Generation Function"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def generate_output_file(final_df: pd.DataFrame, output_filename: str = 'Germany_Impairment_Output.xlsx'):\n", "    \"\"\"\n", "    Generate the final Excel output file with a single sheet formatted for Power BI.\n", "    \"\"\"\n", "    logging.info(f\"Creating output file for Power BI: {output_filename}\")\n", "    try:\n", "        # Define the exact column order required for Power BI\n", "        pbi_columns = [\n", "            'Country', 'SKU', 'Material Description', 'Storage Location', 'Movement type', \n", "            'Quantity moved', 'Plant', 'Price', 'Value moved', 'Entry date', \n", "            'Cumulative Qty', 'Today', 'Months', 'Total Stock', 'Total Value', \n", "            'Assigned', 'Value Assigned', 'Impairment Category', 'Status', 'Week_Num'\n", "        ]\n", "        \n", "        # Ensure all required columns exist, fill missing ones if necessary\n", "        for col in pbi_columns:\n", "            if col not in final_df.columns:\n", "                final_df[col] = None # or a default value\n", "        \n", "        # Select and reorder columns for the final output\n", "        power_bi_df = final_df[pbi_columns]\n", "\n", "        with pd.ExcelWriter(output_filename, engine='openpyxl', datetime_format='YYYY-MM-DD HH:MM:SS') as writer:\n", "            power_bi_df.to_excel(writer, sheet_name='PowerBI_upload', index=False)\n", "        \n", "        logging.info(f\"Excel file '{output_filename}' created successfully with 'PowerBI_upload' sheet.\")\n", "        return output_filename\n", "        \n", "    except Exception as e:\n", "        logging.error(f\"Error generating output file: {e}\")\n", "        traceback.print_exc()\n", "        raise\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Integration and Final Execution"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def run_impairment_analysis():\n", "    \"\"\"\n", "    Main function to run the complete impairment analysis workflow.\n", "    \"\"\"\n", "    try:\n", "        logging.info(\"Starting complete impairment analysis workflow.\")\n", "        \n", "        print(\"📊 Fetching data...\")\n", "        movement_data, stock_data, category_data, status_rules = (\n", "            fetch_movement_data(), fetch_stock_data(), \n", "            fetch_category_data(), fetch_status_rules()\n", "        )\n", "        print(\"    ✅ Data fetching complete.\")\n", "        \n", "        print(\"\\n⚙️ Processing and aging stock data...\")\n", "        aged_stock = process_and_age_stock(movement_data, stock_data, category_data, status_rules)\n", "        print(f\"    ✅ Generated {len(aged_stock)} aged stock layers.\")\n", "        \n", "        print(\"\\n📈 Analysis Results Summary:\")\n", "        total_value = aged_stock['Value Assigned'].sum()\n", "        impaired_value = aged_stock.loc[aged_stock['Status'] == 'Obsolete', 'Value Assigned'].sum()\n", "        print(f\"  - Total Assigned Value: ${total_value:,.2f}\")\n", "        print(f\"  - Total Impaired Value ('Obsolete'): ${impaired_value:,.2f}\")\n", "        print(f\"  - Plants covered: {sorted(aged_stock['Plant'].unique())}\")\n", "        \n", "        return aged_stock\n", "        \n", "    except Exception as e:\n", "        logging.error(f\"Error in run_impairment_analysis: {e}\")\n", "        raise"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-20 19:59:17,963 - INFO - Starting complete impairment analysis workflow.\n", "2025-08-20 19:59:17,965 - INFO - Fetching movement data from Databricks...\n", "2025-08-20 19:59:17,967 - INFO - Establishing connection to Databricks...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "RUNNING COMPLETE IMPAIRMENT ANALYSIS\n", "============================================================\n", "📊 Fetching data...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-20 19:59:20,565 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:20,568 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:20,570 - INFO - Successfully opened session 01f07dd2-1092-1ab2-bf0a-f2bdb43eeb78\n", "2025-08-20 19:59:20,570 - INFO - Successfully connected to Databricks SQL warehouse\n", "2025-08-20 19:59:25,977 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:25,979 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:31,202 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:31,204 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:36,407 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:36,409 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:39,836 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:39,838 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:40,053 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:40,055 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:40,342 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:40,344 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:43,949 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:43,952 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:47,881 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:47,882 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 19:59:51,589 - INFO - Received status code 200 for POST request\n", "2025-08-20 19:59:51,590 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:00:03,097 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:00:03,099 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:00:07,206 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:00:07,207 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:00:10,844 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:00:10,845 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:01:53,905 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:01:53,907 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:01:56,486 - INFO - Closing session 01f07dd2-1092-1ab2-bf0a-f2bdb43eeb78\n", "2025-08-20 20:01:56,719 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:01:56,721 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:01:57,748 - INFO - Successfully fetched 9355806 movement records\n", "2025-08-20 20:01:57,750 - INFO - Fetching stock data from Databricks...\n", "2025-08-20 20:01:57,751 - INFO - Establishing connection to Databricks...\n", "2025-08-20 20:01:59,663 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:01:59,664 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:01:59,666 - INFO - Successfully opened session 01f07dd2-6f6d-1b17-9695-38a80ce8182c\n", "2025-08-20 20:01:59,668 - INFO - Successfully connected to Databricks SQL warehouse\n", "2025-08-20 20:02:01,464 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:02:01,466 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:02:03,961 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:02:03,962 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:02:03,998 - INFO - Closing session 01f07dd2-6f6d-1b17-9695-38a80ce8182c\n", "2025-08-20 20:02:04,244 - INFO - Received status code 200 for POST request\n", "2025-08-20 20:02:04,246 - INFO - HTTP Response with status code 200, message: OK\n", "2025-08-20 20:02:04,254 - INFO - Successfully fetched 72696 stock records\n", "2025-08-20 20:02:04,256 - INFO - Reading category data from Excel file...\n", "2025-08-20 20:02:05,468 - INFO - Successfully loaded 605 category mappings\n", "2025-08-20 20:02:05,469 - INFO - Reading status rules from Excel file...\n", "2025-08-20 20:02:06,136 - INFO - Successfully loaded 2 status rules\n", "2025-08-20 20:02:06,138 - INFO - Starting stock aging process...\n", "2025-08-20 20:02:06,140 - INFO - Step 1: Aggregating positive stock movements\n"]}, {"name": "stdout", "output_type": "stream", "text": ["    ✅ Data fetching complete.\n", "\n", "⚙️ Processing and aging stock data...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-20 20:02:15,845 - INFO - Step 2: Preparing and calculating cumulative quantities\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Debugging 'Quantity moved' column ---\n", "Original DType: object\n", "No data conversion issues found. All values are numeric.\n", "--- End of Debug ---\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-20 20:02:25,142 - INFO - Step 3: Joining with current stock data\n", "2025-08-20 20:02:30,114 - INFO - Step 4: Calculating assigned quantities\n", "2025-08-20 20:02:35,822 - ERROR - Error in process_and_age_stock: unsupported operand type(s) for -: 'float' and 'decimal.Decimal'\n", "2025-08-20 20:02:35,824 - ERROR - Error in run_impairment_analysis: unsupported operand type(s) for -: 'float' and 'decimal.Decimal'\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_31656\\3567623733.py\", line 8, in <module>\n", "    final_aged_stock = run_impairment_analysis()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_31656\\1571659367.py\", line 16, in run_impairment_analysis\n", "    aged_stock = process_and_age_stock(movement_data, stock_data, category_data, status_rules)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_31656\\2431538307.py\", line 60, in process_and_age_stock\n", "    joined_data['Assigned'] = joined_data.apply(calculate_assigned, axis=1)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\miniconda3\\lib\\site-packages\\pandas\\core\\frame.py\", line 10037, in apply\n", "    return op.apply().__finalize__(self, method=\"apply\")\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\miniconda3\\lib\\site-packages\\pandas\\core\\apply.py\", line 837, in apply\n", "    return self.apply_standard()\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\miniconda3\\lib\\site-packages\\pandas\\core\\apply.py\", line 963, in apply_standard\n", "    results, res_index = self.apply_series_generator()\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\miniconda3\\lib\\site-packages\\pandas\\core\\apply.py\", line 979, in apply_series_generator\n", "    results[i] = self.func(v, *self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_31656\\2431538307.py\", line 59, in calculate_assigned\n", "    return r['Quantity moved'] if r['Cumulative Qty'] < r['Total Stock'] else max(0, r['Quantity moved'] - (r['Cumulative Qty'] - r['Total Stock']))\n", "TypeError: unsupported operand type(s) for -: 'float' and 'decimal.Decimal'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "❌ An error occurred during the analysis: unsupported operand type(s) for -: 'float' and 'decimal.Decimal'\n", "============================================================\n"]}], "source": ["# Execute the complete analysis and generate the output file\n", "if __name__ == '__main__':\n", "    print(\"=\"*60)\n", "    print(\"RUNNING COMPLETE IMPAIRMENT ANALYSIS\")\n", "    print(\"=\"*60)\n", "    \n", "    try:\n", "        final_aged_stock = run_impairment_analysis()\n", "        \n", "        if not final_aged_stock.empty:\n", "            print(\"\\n📄 Generating Excel output file...\")\n", "            output_file = generate_output_file(final_aged_stock)\n", "            print(f\"    ✅ Excel output file created successfully: {output_file}\")\n", "            print(\"\\n📋 Sample of Final Aged Stock DataFrame:\")\n", "            print(final_aged_stock.head())\n", "        else:\n", "            print(\"\\n⚠️ Analysis resulted in an empty DataFrame. No output file generated.\")\n", "\n", "        print(\"\\nAnalysis and output generation completed successfully! 🎉\")\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n❌ An error occurred during the analysis: {e}\")\n", "        traceback.print_exc()\n", "    \n", "    print(\"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}