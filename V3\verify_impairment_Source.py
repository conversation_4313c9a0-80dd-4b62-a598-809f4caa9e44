import pandas as pd
import numpy as np

# --- Configuration ---
DETAILED_FILES = [
    'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 09.csv',
    'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 36.csv'
]
SUMMARY_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Material Level Summary.csv'
CATEGORY_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Category 2.csv'

# The definitive list of keywords for material groups to be EXCLUDED from the final sum
EXCLUDED_KEYWORDS = [
    'BEER DISPENSE', 'COOLER', 'FURNITURE', 'EVENT MATERIAL', 
    'TECHNICAL MATERIAL', 'LEIHGUT', 'MOBILE BAR', 'PARASOLS', 
    'FRIDGES', 'DISPENSE COOLER', 'PERFECT DRAFT'
]

# Column names for the detailed MB5B files
COLUMN_NAMES = [
    'Conc', 'Plant', 'Material', 'Material Description', 'Opening Stock', 
    'Total Receipt Qties', 'Issue Quantiti', 'Closing Stock', 'QTES', 
    'CTRL ecart', 'Valeur', 'Valeur/qtes', 'Opening - issue quantity >0', 
    'Impairment', 'NBV', 'Category'
]

def get_filtered_total_from_detailed_sheets(filepaths, category_filepath):
    """
    Loads detailed sheets, applies exclusion logic, and sums the 'Impairment' column.
    """
    print("\n--- Analyzing Detailed Sheets with Exclusion Logic ---")
    try:
        # 1. Load the category mapping file
        categories = pd.read_csv(
            category_filepath, 
            usecols=['Material', 'Material Group Desc.'],
            dtype={'Material': str}
        ).rename(columns={'Material Group Desc.': 'material_group'}).drop_duplicates('Material')
        categories['material_group'] = categories['material_group'].astype(str).str.upper()
        print(f"✅ Loaded category info for {len(categories)} materials.")
    except Exception as e:
        print(f"❌ ERROR: Could not process category file '{category_filepath}': {e}")
        return None

    all_detailed_data = []
    for file in filepaths:
        try:
            # 2. Load the detailed MB5B data
            df = pd.read_csv(file,  names=COLUMN_NAMES, thousands=',', dtype={'Material': str})
            all_detailed_data.append(df)
        except Exception as e:
            print(f"❌ ERROR: Could not process file '{file}': {e}")
            return None
            
    # 3. Combine and process the detailed data
    combined_df = pd.concat(all_detailed_data, ignore_index=True)
    combined_df.dropna(subset=['Material'], inplace=True)
    combined_df['Impairment'] = pd.to_numeric(combined_df['Impairment'], errors='coerce').fillna(0)
    
    # 4. Merge with category info to get the material group for each item
    merged_df = pd.merge(combined_df, categories, on='Material', how='left')
    merged_df['material_group'] = merged_df['material_group'].fillna('UNKNOWN')
    
    # 5. Apply the exclusion logic
    # Create a boolean mask: True if the group should be excluded
    exclusion_mask = merged_df['material_group'].apply(
        lambda group: any(keyword in group for keyword in EXCLUDED_KEYWORDS)
    )
    
    # Invert the mask to keep only the items that should be impaired
    filtered_df = merged_df[~exclusion_mask]
    
    total_impairment = filtered_df['Impairment'].sum()
    
    print(f"✅ Read {len(combined_df)} total records from detailed sheets.")
    print(f"✅ Excluded {exclusion_mask.sum()} records based on material group.")
    print(f"✅ Calculated final impairment on {len(filtered_df)} records.")
    return total_impairment

def get_total_from_summary_report(filepath):
    """
    Parses the summary report to get its grand total for comparison.
    """
    print("\n--- Analyzing Summary Report Sheet ---")
    try:
        df = pd.read_csv(filepath, header=None, dtype=str)
        valid_rows = pd.to_numeric(df[3], errors='coerce').notna()
        df = df[valid_rows]
        df['impairment_value'] = pd.to_numeric(df[3], errors='coerce').fillna(0)
        total_impairment = df['impairment_value'].sum()
        print(f"✅ Read '{filepath}'. Found grand total impairment of: {total_impairment:,.2f}")
        return total_impairment
    except FileNotFoundError:
        print(f"❌ ERROR: File not found: '{filepath}'")
        return None

def main():
    """
    Verifies that the sum of filtered impairment from detailed sheets
    matches the grand total of the final summary report.
    """
    # Get total from the detailed calculation sheets after applying business rules
    detailed_filtered_total = get_filtered_total_from_detailed_sheets(DETAILED_FILES, CATEGORY_FILE)
    
    # Get total from the final summary report
    summary_total = get_total_from_summary_report(SUMMARY_FILE)
    
    if detailed_filtered_total is None or summary_total is None:
        print("\nCould not complete analysis due to file errors.")
        return
        
    print("\n--- Final Verification Result ---")
    print(f"  > Filtered Impairment from Detailed Sheets: {detailed_filtered_total:,.2f}")
    print(f"  > Grand Total from Summary Report:          {summary_total:,.2f}")
    
    if np.isclose(detailed_filtered_total, summary_total, atol=1):
        print("\n✅✅✅ HYPOTHESIS CONFIRMED: The totals match.")
        print("   The logic is now fully understood. The summary report is a filtered view of the detailed calculations.")
    else:
        print("\n❌ HYPOTHESIS FAILED: The totals still do not match.")

if __name__ == "__main__":
    main()

