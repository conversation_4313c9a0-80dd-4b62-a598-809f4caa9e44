# -*- coding: utf-8 -*-
import os
from calendar import monthrange
from datetime import date, timed<PERSON><PERSON>
from typing import Tuple

import pandas as pd
from databricks import sql
from dotenv import load_dotenv

load_dotenv(".env", override=True)

CATALOG_SCHEMA = "brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe"
TABLE_MSEG = f"{CATALOG_SCHEMA}.mseg"
TABLE_MBEW = f"{CATALOG_SCHEMA}.mbew"
TABLE_MBEWH = f"{CATALOG_SCHEMA}.mbewh"

ENV_VARS = ("DATABRICKS_HOST", "DATABRICKS_HTTP_PATH", "DATABRICKS_TOKEN")


def _conn():
    missing = [v for v in ENV_VARS if not os.getenv(v)]
    if missing:
        raise EnvironmentError("Missing env vars: " + ", ".join(missing))
    return sql.connect(
        server_hostname=os.getenv("DATABRICKS_HOST"),
        http_path=os.getenv("DATABRICKS_HTTP_PATH"),
        access_token=os.getenv("DATABRICKS_TOKEN"),
    )


def _prev_year_month(year: int, month: int) -> Tuple[int, int]:
    return (year - 1, 12) if month == 1 else (year, month - 1)


def _next_year_month(year: int, month: int) -> Tuple[int, int]:
    return (year + 1, 1) if month == 12 else (year, month + 1)


def _scalar(cur, sql_stmt: str, default=0.0) -> float:
    cur.execute(sql_stmt)
    pdf = cur.fetchall_arrow().to_pandas()
    return float(pdf.iloc[0, 0]) if not pdf.empty else default


def mb5b_range(matnr: int, plant: str, start_year: int, start_month: int, end_year: int, end_month: int) -> pd.DataFrame:
    """Roll forward from start period to end period (inclusive)."""
    # Opening stock – previous month before start period
    open_y, open_m = _prev_year_month(start_year, start_month)

    # Date boundaries for movements
    first_day = date(start_year, start_month, 1)
    last_day = date(end_year, end_month, monthrange(end_year, end_month)[1])

    with _conn() as conn, conn.cursor() as cur:
        open_q = _scalar(
            cur,
            f"SELECT lbkum FROM {TABLE_MBEWH} WHERE matnr={matnr} AND bwkey='{plant}' "
            f"AND lfgja={open_y} AND lfmon={open_m:02d}"
        )

        close_q_wh = _scalar(
            cur,
            f"SELECT lbkum FROM {TABLE_MBEWH} WHERE matnr={matnr} AND bwkey='{plant}' "
            f"AND lfgja={end_year} AND lfmon={end_month:02d}",
            default=None,
        )
        if close_q_wh is None:
            # if current period not yet in history use MBEW
            close_q = _scalar(
                cur,
                f"SELECT lbkum FROM {TABLE_MBEW} WHERE matnr={matnr} AND bwkey='{plant}'",
                default=0.0,
            )
        else:
            close_q = close_q_wh

        move_sql = (
            f"SELECT SUM(CASE WHEN shkzg='S' THEN menge ELSE 0 END) AS receipts, "
            f"       SUM(CASE WHEN shkzg='H' THEN menge ELSE 0 END) AS issues "
            f"FROM {TABLE_MSEG} WHERE matnr={matnr} AND werks='{plant}' "
            f"AND budat_mkpf>=DATE('{first_day}') AND budat_mkpf<=DATE('{last_day}')"
        )
        cur.execute(move_sql)
        mv = cur.fetchall_arrow().to_pandas().fillna(0)

    receipts = float(mv["receipts"].iloc[0])
    issues = float(mv["issues"].iloc[0])

    return pd.DataFrame(
        {
            "plant": [plant],
            "matnr": [matnr],
            "start": [f"{start_year}-{start_month:02d}"],
            "end": [f"{end_year}-{end_month:02d}"],
            "opening_qty": [open_q],
            "receipts": [receipts],
            "issues": [issues],
            "closing_qty": [close_q],
        }
    )


if __name__ == "__main__":
    import sys

    if len(sys.argv) != 7:
        print(
            "Usage: python mb5b_snapshot.py <MATNR> <PLANT> <START_YEAR> <START_PERIOD> <END_YEAR> <END_PERIOD>"
        )
        sys.exit(1)

    matnr = int(sys.argv[1])
    plant = sys.argv[2]
    sy, sm = int(sys.argv[3]), int(sys.argv[4])
    ey, em = int(sys.argv[5]), int(sys.argv[6])

    df = mb5b_range(matnr, plant, sy, sm, ey, em)
    print(df.to_string(index=False))
