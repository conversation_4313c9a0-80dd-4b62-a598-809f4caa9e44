import pandas as pd
import numpy as np
import logging

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    # --- Step 1: Define the source file paths and sheet names ---
    sot_mapping_file_path = 'DE Temp POCM Calculation P07 2025_TL.xlsx'
    sot_mapping_sheet = 'Glass and others'

    description_source_file_path = 'DE Temp POCM Calculation P07 2025_TL.xlsx'
    description_source_sheet = 'MB5B - 36'

    output_filename = 'Corrected_Impairment_Category_Mapping.csv'

    logging.info(f"Loading Source of Truth (SOT) mapping file: {sot_mapping_file_path} | Sheet: {sot_mapping_sheet}")
    sot_map_df = pd.read_excel(sot_mapping_file_path, sheet_name=sot_mapping_sheet)

    # --- Step 2: Standardize the SOT file ---
    # Rename columns, accounting for potential extra spaces found in the file inspection
    sot_map_df.rename(columns={'Materials': 'SKU', 'Category ': 'Category'}, inplace=True)
    
    # Clean up the data types and remove duplicates
    sot_map_df['SKU'] = pd.to_numeric(sot_map_df['SKU'], errors='coerce')
    sot_map_df.dropna(subset=['SKU'], inplace=True)
    sot_map_df['SKU'] = sot_map_df['SKU'].astype(int)
    sot_map_df['Category'] = sot_map_df['Category'].str.strip()
    
    # Keep only the first category entry for each unique SKU to create a clean map
    corrected_map_df = sot_map_df.drop_duplicates(subset='SKU', keep='first').copy()

    # --- Step 3: Format the categories to match the main script's expectations ---
    # 'Glass' becomes 'GLASSES & CUPS', everything else becomes 'OTHER'
    corrected_map_df['Impairment Category'] = np.where(
        corrected_map_df['Category'].str.upper() == 'GLASS',
        'GLASSES & CUPS',
        'OTHER'
    )
    
    # --- Step 4: Add Material Descriptions for completeness ---
    logging.info(f"Loading descriptions from: {description_source_file_path} | Sheet: {description_source_sheet}")
    # Data starts at row 5 (header=4 corresponds to fifth row)
    desc_df = pd.read_excel(description_source_file_path, sheet_name=description_source_sheet, header=4)
    # Clean column names by stripping extra whitespace
    desc_df.columns = desc_df.columns.str.strip()

    # Standardise column names to expected ones
    if 'Material' in desc_df.columns:
        desc_df.rename(columns={'Material': 'SKU'}, inplace=True)
    elif 'MATNR' in desc_df.columns:  # fallback SAP field name
        desc_df.rename(columns={'MATNR': 'SKU'}, inplace=True)

    if 'Material Description' not in desc_df.columns and 'MAKTX' in desc_df.columns:
        desc_df.rename(columns={'MAKTX': 'Material Description'}, inplace=True)

    # Select only the required columns now that they are standardised
    desc_df = desc_df[['SKU', 'Material Description']].drop_duplicates(subset='SKU', keep='first')
    
    # Merge to add the descriptions to our final mapping file
    final_mapping_df = pd.merge(corrected_map_df[['SKU', 'Impairment Category']], desc_df, on='SKU', how='left')

    # Reorder columns to the desired format
    final_mapping_df = final_mapping_df[['SKU', 'Material Description', 'Impairment Category']]
    
    # --- Step 5: Save the final file ---
    final_mapping_df.to_csv(output_filename, index=False)
    
    logging.info("--------------------------------------------------")
    logging.info(f"✅ Successfully created the corrected mapping file: {output_filename}")
    logging.info("You can now use this file in your main Python script.")
    logging.info("--------------------------------------------------")

except FileNotFoundError as e:
    logging.error(f"CRITICAL ERROR: A file was not found. Please ensure '{e.filename}' is in the same directory as the script.")
except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")