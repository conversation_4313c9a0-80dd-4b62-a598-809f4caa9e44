import pandas as pd
from datetime import date
import logging
from databricks import sql

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """
    Establish and return a connection to the Databricks SQL warehouse.
    """
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        # It is recommended to use a secure way to store and access your token
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_databricks_query(query: str, connection=None) -> pd.DataFrame:
    """
    Execute a SQL query on Databricks and return results as a DataFrame.
    """
    close_conn = False
    if connection is None:
        connection = get_databricks_connection()
        close_conn = True
    try:
        logging.info("Executing the transactional query...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        logging.info(f"Query successful. Fetched {len(data)} rows.")
        return pd.DataFrame(data, columns=columns)
    finally:
        if close_conn and connection:
            connection.close()
            logging.info("Databricks connection closed.")

def get_german_inventory_query(report_date: str) -> str:
    """
    Generates the SQL query to get the inventory data for Germany,
    with different date ranges for glass and other materials.
    """
    glass_start_date = '2022-08-31'
    other_start_date = '2024-11-01'
    
    return f"""
    SELECT
        Subtable.SKU,
        Subtable.`Material Description`,
        Subtable.`Plant Code`,
        Subtable.`Movement type`,
        Subtable.`Entry Date`,
        Subtable.Quantity,
        Subtable.CumSum_ById,
        Subtable.Today,
        Subtable.Days,
        Subtable.`Moving Price`,
        Subtable.Per,
        Subtable.`Total Stock`,
        Subtable.`Total Stock Value`,
        Subtable.Assigned,
        Subtable.Assigned * Subtable.`Moving Price` / NULLIF(Subtable.Per, 0) AS `Assigned Value`
    FROM
    (
        SELECT
            d.SKU,
            f.maktx AS `Material Description`,
            d.`Plant Code`,
            d.`Movement type`,
            d.`Entry Date`,
            d.Quantity,
            SUM(d.Quantity) OVER (
                PARTITION BY
                    d.`Plant Code`,
                    d.SKU
                ORDER BY
                    d.`Entry Date` DESC,
                    d.`Movement type`
            ) AS CumSum_ById,
            d.Today,
            d.Days,
            e.verpr AS `Moving Price`,
            e.peinh AS Per,
            e.lbkum AS `Total Stock`,
            e.salk3 AS `Total Stock Value`,
            CASE
                WHEN (
                    SUM(d.Quantity) OVER (
                        PARTITION BY
                            d.`Plant Code`,
                            d.SKU
                        ORDER BY
                            d.`Entry Date` DESC,
                            d.`Movement type`
                    )
                ) < e.lbkum THEN d.Quantity
                ELSE d.Quantity - (
                    (
                        SUM(d.Quantity) OVER (
                            PARTITION BY
                                d.`Plant Code`,
                                d.SKU
                            ORDER BY
                                d.`Entry Date` DESC,
                                d.`Movement type`
                        )
                    ) - e.lbkum
                )
            END AS Assigned
        FROM
        (
            SELECT
                SKU,
                `Plant Code`,
                `Movement type`,
                `Entry Date`,
                SUM(c.`Quantity Movement`) AS Quantity,
                current_date() AS Today,
                Datediff(current_date(), `Entry Date`) AS Days
            FROM
            (
                SELECT
                    a.bwart AS `Movement Type`,
                    a.matnr AS SKU,
                    a.werks AS `Plant Code`,
                    CASE
                        WHEN a.shkzg = 'H' THEN -1 * a.menge
                        WHEN a.shkzg = 'S' THEN 1 * a.menge
                    END AS `Quantity Movement`,
                    a.cpudt_mkpf AS `Entry Date`
                FROM
                    brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg AS a
                    LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS f ON a.matnr = f.matnr AND f.spras = 'E'
                WHERE
                    a.werks LIKE 'DE%'
                    AND a.matnr BETWEEN '000000000007500000' AND '000000000007600000'
                    AND (
                        (f.maktx LIKE '%GLASS%' AND a.cpudt_mkpf BETWEEN '{glass_start_date}' AND '{report_date}')
                        OR
                        (f.maktx NOT LIKE '%GLASS%' AND a.cpudt_mkpf BETWEEN '{other_start_date}' AND '{report_date}')
                    )
            ) AS c
            WHERE
                `Quantity Movement` > 0
            GROUP BY
                SKU,
                `Plant Code`,
                `Movement type`,
                `Entry Date`
        ) AS d
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS e ON d.SKU = e.matnr AND d.`Plant Code` = e.bwkey
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS f ON d.SKU = f.matnr AND f.spras = 'E'
    ) AS Subtable
    WHERE
        Assigned > 0
    ORDER BY
        `Entry Date` DESC
    """

def transform_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Transforms the queried data to match the target Power BI structure.
    """
    # Add 'Country' column
    df['Country'] = 'DE'

    # Add other placeholder columns as per the target structure.
    # You can customize the logic for these columns as needed.
    df['Storage Location'] = ''  # Placeholder
    df['Impairment Category'] = 'OTHER' # Placeholder
    df['Status'] = 'Fast Mover' # Placeholder
    df['Week_Num'] = pd.to_datetime(df['Entry Date']).dt.isocalendar().week.apply(lambda x: f'W{x}')


    # Reorder columns to match the target structure
    target_columns = [
        'Country', 'SKU', 'Material Description', 'Storage Location', 'Movement type',
        'Quantity', 'Plant Code', 'Moving Price', 'Assigned Value', 'Entry Date',
        'CumSum_ById', 'Today', 'Days', 'Total Stock', 'Total Stock Value',
        'Assigned', 'Value Assigned', 'Impairment Category', 'Status', 'Week_Num'
    ]
    # Rename columns to match the target
    df = df.rename(columns={
        'Quantity': 'Quantity moved',
        'Plant Code': 'Plant',
        'Moving Price': 'Price',
        'Assigned Value': 'Value moved',
        'Entry Date': 'Entry date',
        'CumSum_ById': 'Cumulative Qty',
        'Days': 'Months', # Assuming 'Days' can be used for 'Months' or you can calculate it
        'Total Stock Value': 'Total Value'
    })


    # Reorder and select the final columns, handling missing ones
    final_df = pd.DataFrame()
    for col in target_columns:
        if col in df.columns:
            final_df[col] = df[col]
        else:
            final_df[col] = None # Or some other default value

    return final_df


if __name__ == '__main__':
    try:
        # Set the report date based on the user's extraction date
        report_date_str = '2025-07-31'

        logging.info(f"Running inventory report for Germany with extraction date: {report_date_str}")

        # Get and execute the query
        inventory_query = get_german_inventory_query(report_date=report_date_str)
        inventory_df = execute_databricks_query(inventory_query)

        if not inventory_df.empty:
            # Transform the data
            final_df = transform_data(inventory_df)

            # Save to CSV
            output_filename = f'German_PBI_Inventory_Report_{report_date_str}.csv'
            final_df.to_csv(output_filename, index=False, date_format='%Y-%m-%d')
            logging.info(f"Successfully created the report: {output_filename}")
        else:
            logging.info("No data returned from the query.")

    except Exception as e:
        logging.error(f"An error occurred: {e}")