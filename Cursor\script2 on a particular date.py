import pandas as pd
from datetime import date
import logging
from databricks import sql

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """
    Establish and return a connection to the Databricks SQL warehouse.
    """
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_databricks_query(query: str, connection=None) -> pd.DataFrame:
    """
    Execute a SQL query on Databricks and return results as a DataFrame.
    """
    close_conn = False
    if connection is None:
        connection = get_databricks_connection()
        close_conn = True
    try:
        logging.info("Executing the final transactional query with material filter...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        logging.info(f"Query successful. Fetched {len(data)} rows.")
        return pd.DataFrame(data, columns=columns)
    finally:
        if close_conn and connection:
            connection.close()
            logging.info("Databricks connection closed.")

def get_detailed_stock_query(report_date):
    """
    Returns a fully automated query that provides a detailed, transactional output
    with correctly calculated total stock and status for any given date.
    This version is permanently filtered for a specific material range.
    """
    return f"""
    WITH
    -- 1. Calculate the final stock position by summing all movements up to the report_date
    StockAsOfDate AS (
        SELECT
            matnr,
            werks,
            SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS TotalStock
        FROM
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        WHERE
            werks LIKE 'DE%'
            AND cpudt_mkpf <= '{report_date}'
            -- =============================================================================
            -- PERMANENT FILTER ADDED
            AND matnr BETWEEN '000000000007546978' AND '000000000007597095'
            -- =============================================================================
        GROUP BY
            matnr, werks
    ),

    -- 2. Get the latest valuation and description data for each material
    ValuationData AS (
        SELECT
            mbew.matnr,
            mbew.bwkey,
            MAX(makt.maktx) AS MaterialDescription,
            MAX(mbew.verpr / NULLIF(mbew.peinh, 0)) AS UnitPrice
        FROM
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS mbew
        LEFT JOIN
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS makt
            ON mbew.matnr = makt.matnr AND makt.spras = 'E'
        GROUP BY
            mbew.matnr, mbew.bwkey
    ),

    -- 3. Get all individual movements, which will form the base of our report
    AllMovements AS (
        SELECT
            mseg.matnr,
            mseg.werks,
            mseg.lgort,
            mseg.bwart,
            mseg.cpudt_mkpf AS EntryDate,
            CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END AS NetQuantity,
            mara.matkl AS MaterialGroup
        FROM
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        LEFT JOIN
             brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mara AS mara ON mseg.matnr = mara.matnr
        WHERE
            mseg.werks LIKE 'DE%'
            -- =============================================================================
            -- PERMANENT FILTER ADDED
            AND mseg.matnr BETWEEN '000000000007546978' AND '000000000007597095'
            -- =============================================================================
    )

    -- 4. Final join to combine individual movements with the correct totals and all details
    SELECT
        'DE' AS Country,
        TRIM(LEADING '0' FROM mov.matnr) AS SKU,
        val.MaterialDescription AS `Material Description`,
        mov.lgort AS `Storage Location`,
        mov.bwart AS `Movement type`,
        mov.NetQuantity AS `Quantity moved`,
        mov.werks AS Plant,
        val.UnitPrice AS Price,
        (mov.NetQuantity * val.UnitPrice) AS `Value moved`,
        mov.EntryDate AS `Entry date`,
        CAST('{report_date}' AS DATE) AS Today,
        CAST(DATEDIFF(day, mov.EntryDate, '{report_date}') / 30 AS INT) AS Months,
        stock.TotalStock AS `Total Stock`,
        (stock.TotalStock * val.UnitPrice) AS `Total Value`,
        CASE WHEN mov.MaterialGroup = 'Z001' THEN 'Glass' ELSE 'Others' END AS `Impairment Category`,
        
        -- Automated Status Calculation based on aging rules
        CASE
            WHEN mov.MaterialGroup = 'Z001' THEN -- Rules for Glass
                CASE
                    WHEN DATEDIFF(day, mov.EntryDate, '{report_date}') / 30 >= 36 THEN 'Obsolete'
                    WHEN DATEDIFF(day, mov.EntryDate, '{report_date}') / 30 >= 10 THEN 'Alarm'
                    WHEN DATEDIFF(day, mov.EntryDate, '{report_date}') / 30 >= 3 THEN 'Slow Mover'
                    ELSE 'Fast Mover'
                END
            ELSE -- Rules for Others
                CASE
                    WHEN DATEDIFF(day, mov.EntryDate, '{report_date}') / 30 >= 9 THEN 'Obsolete'
                    WHEN DATEDIFF(day, mov.EntryDate, '{report_date}') / 30 >= 6 THEN 'Alarm'
                    WHEN DATEDIFF(day, mov.EntryDate, '{report_date}') / 30 >= 3 THEN 'Slow Mover'
                    ELSE 'Fast Mover'
                END
        END AS Status,
        
        'W' || LPAD(CAST(WEEKOFYEAR('{report_date}') AS STRING), 2, '0') AS Week_Num

    FROM
        AllMovements AS mov
    -- Join to get the correct total stock calculated as of the report date
    INNER JOIN
        StockAsOfDate AS stock ON mov.matnr = stock.matnr AND mov.werks = stock.werks
    -- Join to get valuation and description
    LEFT JOIN
        brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.t001w AS plant_map
        ON mov.werks = plant_map.werks
    LEFT JOIN
        ValuationData AS val
        ON mov.matnr = val.matnr AND plant_map.bwkey = val.bwkey
    WHERE
        mov.NetQuantity != 0
    ORDER BY
        mov.werks, mov.matnr, mov.EntryDate DESC;
    """

if __name__ == '__main__':
    try:
        # You can set this to any date you want.
        # For today's date, use: date.today().strftime('%Y-%m-%d')
        report_date_str = date.today().strftime('%Y-%m-%d') 
        logging.info(f"Running automated stock report for date: {report_date_str}")
        
        # Get and execute the query for the specified date
        stock_query = get_detailed_stock_query(report_date=report_date_str)
        final_df = execute_databricks_query(stock_query)

        if not final_df.empty:
            output_filename = f'PBI_Detailed_Stock_Report_{report_date_str}_Filtered.csv'
            final_df.to_csv(output_filename, index=False, date_format='%Y-%m-%d')
            logging.info(f"Successfully created the detailed, filtered report: {output_filename}")
        else:
            logging.warning("No data was returned from the query. The output file was not created.")

    except Exception as e:
        logging.critical(f"A critical error occurred during script execution: {e}")